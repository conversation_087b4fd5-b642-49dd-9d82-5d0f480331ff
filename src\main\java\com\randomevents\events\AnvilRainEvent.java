package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.FallingBlockEntity;
import net.minecraft.world.level.block.Blocks;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class AnvilRainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static volatile boolean eventActive = false;
    private static final int MAX_ANVILS_PER_WAVE = 20; // Limit anvils per wave
    private static final int MAX_TOTAL_ANVILS = 100; // Total limit for entire event
    private static int totalAnvilsSpawned = 0;
    
    @Override
    public String getId() {
        return "anvil_rain";
    }
    
    @Override
    public String getName() {
        return "§8§lAnvil Rain!";
    }
    
    @Override
    public String getDescription() {
        return "Massive amounts of anvils fall from the sky around all players for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Reset counters and activate event
        eventActive = true;
        totalAnvilsSpawned = 0;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lYou don't wanna look up... §8Something heavy is coming!")));
        }
        
        // Wait 3 seconds then start the anvil rain
        scheduler.schedule(() -> {
            // Anvil rain for 30 seconds (every 1 second) with safety limits
            for (int i = 0; i < 30; i++) { // 30 iterations over 30 seconds
                scheduler.schedule(() -> {
                    if (!eventActive || totalAnvilsSpawned >= MAX_TOTAL_ANVILS) {
                        return; // Stop if event ended or hit limit
                    }

                    int anvilsThisWave = 0;
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        if (anvilsThisWave >= MAX_ANVILS_PER_WAVE || totalAnvilsSpawned >= MAX_TOTAL_ANVILS) {
                            break; // Stop this wave if we hit limits
                        }

                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Drop only 2-4 anvils around each player (much safer)
                        int anvilCount = Math.min(2 + random.nextInt(3), MAX_ANVILS_PER_WAVE - anvilsThisWave);
                        for (int j = 0; j < anvilCount && totalAnvilsSpawned < MAX_TOTAL_ANVILS; j++) {
                            // Random position around player (smaller area for safety)
                            int x = playerPos.getX() + random.nextInt(20) - 10; // 20 block radius (reduced)
                            int z = playerPos.getZ() + random.nextInt(20) - 10;
                            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 15 + random.nextInt(10); // Lower height

                            BlockPos anvilPos = new BlockPos(x, y, z);

                            // Create falling anvil with safety checks
                            if (level.isInWorldBounds(anvilPos)) {
                                try {
                                    // Use the original fall method but then modify the entity
                                    FallingBlockEntity fallingAnvil = FallingBlockEntity.fall(level, anvilPos, Blocks.ANVIL.defaultBlockState());
                                    if (fallingAnvil != null) {
                                        // Enable damage from falling anvils
                                        fallingAnvil.setHurtsEntities(2.0f, 40); // 2 damage per block fallen, max 40 damage

                                        // Make anvils fall faster and more dramatically
                                        fallingAnvil.setDeltaMovement(
                                            (random.nextDouble() - 0.5) * 0.2, // Reduced horizontal movement
                                            -0.8, // Reduced fall speed for stability
                                            (random.nextDouble() - 0.5) * 0.2
                                        );

                                        anvilsThisWave++;
                                        totalAnvilsSpawned++;
                                        // Note: The entity is already added to the level by FallingBlockEntity.fall()
                                    }
                                } catch (Exception e) {
                                    // Silently handle any entity creation errors to prevent crashes
                                }
                            }
                        }
                    }
                    
                    // Show periodic dramatic messages
                    if (random.nextInt(15) == 0) { // ~6.7% chance each drop
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] anvilMessages = {
                                "§8§lANVILS EVERYWHERE!",
                                "§7§lTHE SKY IS FALLING!",
                                "§8§lMETAL DEATH FROM ABOVE!",
                                "§7§lRUN FOR COVER!",
                                "§8§lANVIL APOCALYPSE!",
                                "§7§lHEAVY METAL RAIN!",
                                "§8§lDON'T LOOK UP!"
                            };
                            String message = anvilMessages[random.nextInt(anvilMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 1000L, TimeUnit.MILLISECONDS); // Every 1 second (safer)
            }

            // Show end message after 30 seconds and deactivate event
            scheduler.schedule(() -> {
                eventActive = false;
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe anvil storm subsides... §8(" + totalAnvilsSpawned + " anvils total)")));
                }
            }, 30L, TimeUnit.SECONDS);
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
