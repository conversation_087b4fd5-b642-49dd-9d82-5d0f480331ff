package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LightningBolt;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LightningStormEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "lightning_storm";
    }
    
    @Override
    public String getName() {
        return "§e§lLightning Storm!";
    }
    
    @Override
    public String getDescription() {
        return "Lightning strikes randomly around all players for 15 seconds, then an intense finale";
    }

    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§8§lThe skies are angry...")));
        }

        // Wait 2 seconds then start the lightning storm
        scheduler.schedule(() -> {
            // Strike lightning every 2 seconds for 15 seconds (reduced from 30)
            for (int i = 0; i < 7; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Strike lightning in a 20 block radius around the player
                    int x = playerPos.getX() + random.nextInt(40) - 20;
                    int z = playerPos.getZ() + random.nextInt(40) - 20;
                    int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);

                    BlockPos strikePos = new BlockPos(x, y, z);
                    LightningBolt lightning = EntityType.LIGHTNING_BOLT.create(level);
                    if (lightning != null) {
                        lightning.moveTo(strikePos.getX(), strikePos.getY(), strikePos.getZ());
                        lightning.setVisualOnly(true); // Prevents fire and damage
                        level.addFreshEntity(lightning);
                    }
                }
            }, i * 2L, TimeUnit.SECONDS);
        }

        // After 15 seconds, show "The storm is over..." message
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7The storm is over... §8But something feels wrong...")));
            }
        }, 15L, TimeUnit.SECONDS);

        // 3 seconds later (18 seconds total), start intense lightning finale
        scheduler.schedule(() -> {
            // First, guarantee each player gets hit by lightning 3 times
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Strike lightning directly on the player 3 times with slight delays
                for (int hitCount = 0; hitCount < 3; hitCount++) {
                    scheduler.schedule(() -> {
                        LightningBolt guaranteedLightning = EntityType.LIGHTNING_BOLT.create(level);
                        if (guaranteedLightning != null) {
                            guaranteedLightning.moveTo(playerPos.getX(), playerPos.getY(), playerPos.getZ());
                            guaranteedLightning.setVisualOnly(false); // Allow damage for dramatic effect
                            level.addFreshEntity(guaranteedLightning);
                        }
                    }, hitCount * 500L, TimeUnit.MILLISECONDS); // 0.5 seconds between each hit
                }
            }

            // Then continue with intense lightning finale - many strikes for 5 seconds
            for (int i = 0; i < 25; i++) { // 25 strikes over 5 seconds
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Strike 3-5 lightning bolts per player
                        int strikeCount = 3 + random.nextInt(3);
                        for (int j = 0; j < strikeCount; j++) {
                            int x = playerPos.getX() + random.nextInt(60) - 30; // Larger area
                            int z = playerPos.getZ() + random.nextInt(60) - 30;
                            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);

                            BlockPos strikePos = new BlockPos(x, y, z);
                            LightningBolt lightning = EntityType.LIGHTNING_BOLT.create(level);
                            if (lightning != null) {
                                lightning.moveTo(strikePos.getX(), strikePos.getY(), strikePos.getZ());
                                lightning.setVisualOnly(true); // Prevents fire and damage for random strikes
                                level.addFreshEntity(lightning);
                            }
                        }
                    }
                }, i * 200L, TimeUnit.MILLISECONDS); // Every 200ms for intense effect
            }
        }, 18L, TimeUnit.SECONDS);

        }, 2L, TimeUnit.SECONDS); // 2 second delay after warning
    }
}
