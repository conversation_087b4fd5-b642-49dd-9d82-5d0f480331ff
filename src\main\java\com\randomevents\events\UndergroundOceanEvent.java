package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

public class UndergroundOceanEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "underground_ocean";
    }
    
    @Override
    public String getName() {
        return "§1§lUnderground Ocean!";
    }
    
    @Override
    public String getDescription() {
        return "Players stand on 1 block while deep water surrounds them everywhere";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lThe underground is flooding! §7Water is rising from below!")));
        }

        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Show start message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lCreating underground ocean around you...")));

            // Fill EVERYTHING around the player with DEEP water (100x100 area, 50 blocks deep)
            int startY = Math.max(playerPos.getY() - 50, level.getMinBuildHeight());
            int endY = playerPos.getY() - 2; // Water goes up to 2 blocks below player

            // Fill water in ALL directions around the player
            for (int x = -50; x <= 50; x++) {
                for (int z = -50; z <= 50; z++) {
                    // Skip ONLY the exact block directly under the player (0,0 relative position)
                    if (x == 0 && z == 0) {
                        // For the player's column, only place the stone block directly under them
                        BlockPos underPlayer = new BlockPos(playerPos.getX(), playerPos.getY() - 1, playerPos.getZ());
                        level.setBlock(underPlayer, Blocks.STONE.defaultBlockState(), 3);
                        continue;
                    }

                    // Fill ALL other positions with water
                    for (int y = startY; y <= endY; y++) {
                        BlockPos waterPos = new BlockPos(playerPos.getX() + x, y, playerPos.getZ() + z);

                        if (level.isInWorldBounds(waterPos)) {
                            // Replace almost everything with water (except bedrock and important blocks)
                            if (!level.getBlockState(waterPos).is(Blocks.BEDROCK) &&
                                !level.getBlockState(waterPos).is(Blocks.SPAWNER) &&
                                !level.getBlockState(waterPos).is(Blocks.END_PORTAL) &&
                                !level.getBlockState(waterPos).is(Blocks.END_PORTAL_FRAME)) {
                                level.setBlock(waterPos, Blocks.WATER.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }

            // Also fill water in the immediate surrounding area at the same level as the player
            // This ensures water is on ALL sides, not just below
            for (int x = -5; x <= 5; x++) {
                for (int z = -5; z <= 5; z++) {
                    // Skip the player's exact position
                    if (x == 0 && z == 0) continue;

                    BlockPos sidePos = new BlockPos(playerPos.getX() + x, playerPos.getY() - 1, playerPos.getZ() + z);
                    if (level.isInWorldBounds(sidePos)) {
                        level.setBlock(sidePos, Blocks.WATER.defaultBlockState(), 3);
                    }
                }
            }

            // Show completion message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§1§lYou're on 1 block above DEEP water on ALL sides! §7Don't fall in!")));
        }
    }
}
