package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class BackwardsControlsEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean backwardsActive = false;
    
    @Override
    public String getId() {
        return "backwards_controls";
    }
    
    @Override
    public String getName() {
        return "§c§lLose Control!";
    }
    
    @Override
    public String getDescription() {
        return "All movement controls are reversed for 20 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        backwardsActive = true;
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYour controls feel... wrong...")));
        }
        
        // Register event listener
        MinecraftForge.EVENT_BUS.register(new BackwardsEventHandler());

        // Show single consistent message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lI feel like I can't control myself...")));
        }

        // EXTREME backwards controls using very frequent checks
        for (int i = 0; i < 800; i++) { // 20 seconds * 40 checks per second = 800 checks
            scheduler.schedule(() -> {
                if (backwardsActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        Vec3 motion = player.getDeltaMovement();

                        // AGGRESSIVE backwards control - reverse ALL movement with strong amplification
                        if (Math.abs(motion.x) > 0.0001 || Math.abs(motion.z) > 0.0001) {
                            // Reverse movement direction with even stronger amplification
                            player.setDeltaMovement(-motion.x * 3.5, motion.y, -motion.z * 3.5);

                            // Also teleport player slightly backwards to make it more obvious
                            Vec3 currentPos = player.position();
                            Vec3 backwardsOffset = new Vec3(-motion.x * 0.1, 0, -motion.z * 0.1);
                            player.teleportTo(currentPos.x + backwardsOffset.x, currentPos.y, currentPos.z + backwardsOffset.z);
                        }

                        // Frequent view rotation for maximum confusion
                        if (Math.random() < 0.3) { // 30% chance per check
                            float currentYaw = player.getYRot();
                            player.setYRot(currentYaw + (float)(Math.random() - 0.5) * 90);
                        }

                        // Random small teleports to simulate control chaos
                        if (Math.random() < 0.05) { // 5% chance
                            Vec3 pos = player.position();
                            double offsetX = (Math.random() - 0.5) * 0.5;
                            double offsetZ = (Math.random() - 0.5) * 0.5;
                            player.teleportTo(pos.x + offsetX, pos.y, pos.z + offsetZ);
                        }

                        // No more random messages - keep the single consistent message
                    }
                }
            }, i * 25L, TimeUnit.MILLISECONDS); // Every 25ms (twice per tick) for maximum effect
        }

        // Stop backwards effect after 20 seconds
        scheduler.schedule(() -> {
            backwardsActive = false;
            MinecraftForge.EVENT_BUS.unregister(BackwardsEventHandler.class);

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lControls are back to normal!")));
            }
        }, 20L, TimeUnit.SECONDS);
    }
    
    public static class BackwardsEventHandler {
        @SubscribeEvent
        public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!backwardsActive || !(event.getEntity() instanceof ServerPlayer player)) return;

            Vec3 motion = player.getDeltaMovement();

            // EXTREME backwards control - reverse all movement aggressively
            if (Math.abs(motion.x) > 0.001 || Math.abs(motion.z) > 0.001) {
                // Reverse movement direction with even stronger amplification
                player.setDeltaMovement(-motion.x * 4.0, motion.y, -motion.z * 4.0);

                // Constant view rotation for maximum confusion
                if (player.level().getGameTime() % 3 == 0) { // Every 3 ticks
                    player.setYRot(player.getYRot() + 45 + (float)(Math.random() * 90));
                }

                // Add random pitch changes too
                if (player.level().getGameTime() % 5 == 0) { // Every 5 ticks
                    player.setXRot(player.getXRot() + (float)(Math.random() - 0.5) * 30);
                }
            }
        }
    }
}
