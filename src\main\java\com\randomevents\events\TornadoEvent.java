package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;

import net.minecraft.world.phys.Vec3;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class TornadoEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    
    @Override
    public String getId() {
        return "tornado";
    }
    
    @Override
    public String getName() {
        return "§8§lTornado!";
    }
    
    @Override
    public String getDescription() {
        return "Spinning vortex picks up players and entities for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§8§lThe wind picks up... §7A tornado is forming!")));
        }
        
        // Phase 1: Lift players into the air and make them whirl (20 seconds)
        for (int i = 0; i < 20; i++) {
            final int tick = i;
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();

                    // Lift player high into the air with spinning motion
                    double angle = tick * 0.5; // Spinning angle
                    double radius = 5.0; // Radius of spinning

                    // Calculate spinning position
                    double spinX = Math.cos(angle) * radius;
                    double spinZ = Math.sin(angle) * radius;
                    double liftY = 2.0; // Strong upward force

                    // Apply tornado forces
                    player.setDeltaMovement(spinX, liftY, spinZ);

                    // Spin the player's view rapidly
                    player.setYRot(player.getYRot() + 30);
                    player.setXRot(player.getXRot() + 10);

                    // Show whirling messages
                    if (tick % 5 == 0) { // Every 5 seconds
                        String[] tornadoMessages = {
                            "§8§lYou're spinning in the tornado!",
                            "§7§lThe wind is too strong!",
                            "§8§lYou're whirling through the air!",
                            "§7§lYou can't control your movement!"
                        };
                        String message = tornadoMessages[ThreadLocalRandom.current().nextInt(tornadoMessages.length)];
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal(message)));
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }

        // Phase 2: Gentle descent (10 seconds)
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe tornado is weakening... §aYou're being lowered safely!")));
            }

            // Gentle descent for 10 seconds
            for (int i = 0; i < 10; i++) {
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        // Gentle downward motion
                        Vec3 motion = player.getDeltaMovement();
                        player.setDeltaMovement(motion.x * 0.8, -0.5, motion.z * 0.8); // Slow descent

                        // Reduce spinning
                        player.setYRot(player.getYRot() + 5); // Much slower spinning
                    }
                }, i * 1L, TimeUnit.SECONDS);
            }
        }, 20L, TimeUnit.SECONDS);

        // Phase 3: Safe landing
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                // Ensure safe landing
                player.setDeltaMovement(0, 0, 0);
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lYou've been safely placed on the ground!")));
            }
        }, 30L, TimeUnit.SECONDS);
    }
}
