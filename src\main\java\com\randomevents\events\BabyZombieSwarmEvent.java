package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.Zombie;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class BabyZombieSwarmEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "baby_zombie_swarm";
    }
    
    @Override
    public String getName() {
        return "§2§lBaby Zombie Swarm!";
    }
    
    @Override
    public String getDescription() {
        return "100 baby zombies spawn around players - they're fast and small!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<Zombie> spawnedZombies = new ArrayList<>();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§2§lTiny footsteps approach... §7Run!")));
        }
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Spawn 100 baby zombies around each player
            for (int i = 0; i < 100; i++) {
                int x = playerPos.getX() + random.nextInt(40) - 20;
                int z = playerPos.getZ() + random.nextInt(40) - 20;
                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
                
                BlockPos spawnPos = new BlockPos(x, y, z);
                
                // Make sure the spawn position is safe
                if (level.getBlockState(spawnPos).isAir() && 
                    level.getBlockState(spawnPos.above()).isAir() &&
                    !level.getBlockState(spawnPos.below()).isAir()) {
                    
                    Zombie zombie = EntityType.ZOMBIE.create(level);
                    if (zombie != null) {
                        zombie.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                        
                        // Make it a baby zombie (faster and smaller)
                        zombie.setBaby(true);
                        
                        // Make them extra fast
                        zombie.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.MOVEMENT_SPEED)
                            .setBaseValue(0.35); // Faster than normal zombies
                        
                        level.addFreshEntity(zombie);
                        spawnedZombies.add(zombie);
                    }
                }
            }
        }
        
        // Remove all baby zombies after 3 minutes
        scheduler.schedule(() -> {
            for (Zombie zombie : spawnedZombies) {
                if (zombie.isAlive()) {
                    zombie.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                }
            }
        }, 180L, TimeUnit.SECONDS);
    }
}
