package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class InventoryBombEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Map<ServerPlayer, Integer> playerTNTCount = new HashMap<>();
    private static boolean bombActive = false;
    
    @Override
    public String getId() {
        return "inventory_bomb";
    }
    
    @Override
    public String getName() {
        return "§c§lInventory Bomb!";
    }
    
    @Override
    public String getDescription() {
        return "Players get full inventory of TNT - drop them all within 25 seconds or EXPLODE!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        bombActive = true;
        playerTNTCount.clear();
        
        // Show dramatic warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§l§kINVENTORY BOMB!§r §4Your inventory is filling with TNT!")));
        }
        
        // Wait 2 seconds then fill inventories with TNT
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                // Fill inventory with TNT but protect important items
                int tntCount = 0;

                // Check main inventory (slots 9-35) and hotbar (slots 0-8)
                for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
                    ItemStack currentItem = player.getInventory().getItem(i);

                    // Don't replace important items
                    if (shouldProtectItem(currentItem)) {
                        continue; // Skip this slot, keep the important item
                    }

                    // Replace with TNT
                    player.getInventory().setItem(i, new ItemStack(Items.TNT, 64));
                    tntCount += 64;
                }

                // Don't touch offhand - it often contains totems
                // ItemStack offhandItem = player.getOffhandItem();
                // We leave offhand completely alone

                playerTNTCount.put(player, tntCount);
                
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lDROP ALL TNT IN 25 SECONDS OR EXPLODE! §4" + tntCount + " TNT to drop!")));
            }
        }, 2L, TimeUnit.SECONDS);

        // Check every second for 25 seconds
        for (int i = 1; i <= 25; i++) {
            final int secondsLeft = 26 - i;
            scheduler.schedule(() -> {
                if (bombActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        // Count remaining TNT in inventory
                        int remainingTNT = 0;
                        for (int slot = 0; slot < player.getInventory().getContainerSize(); slot++) {
                            ItemStack stack = player.getInventory().getItem(slot);
                            if (stack.is(Items.TNT)) {
                                remainingTNT += stack.getCount();
                            }
                        }
                        
                        if (remainingTNT > 0) {
                            if (secondsLeft > 1) {
                                // Show countdown
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§c§l" + secondsLeft + " SECONDS LEFT! §4Drop " + remainingTNT + " TNT or EXPLODE!")));
                            } else {
                                // EXPLODE!
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§c§l§kTIME'S UP!§r §4§lYOU EXPLODE!")));
                                
                                // Create massive explosion at player location
                                player.serverLevel().explode(null, 
                                    player.getX(), player.getY(), player.getZ(), 
                                    8.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.BLOCK);
                                
                                // Clear their inventory of TNT (they exploded)
                                for (int slot = 0; slot < player.getInventory().getContainerSize(); slot++) {
                                    ItemStack stack = player.getInventory().getItem(slot);
                                    if (stack.is(Items.TNT)) {
                                        player.getInventory().setItem(slot, ItemStack.EMPTY);
                                    }
                                }
                            }
                        } else {
                            // Player successfully dropped all TNT
                            if (secondsLeft > 1) {
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§a§lSAFE! §7You dropped all the TNT in time!")));
                            }
                        }
                    }
                }
            }, (2 + i) * 1L, TimeUnit.SECONDS);
        }
        
        // End event after 27 seconds
        scheduler.schedule(() -> {
            bombActive = false;
            playerTNTCount.clear();

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lInventory bomb event ended!")));
            }
        }, 27L, TimeUnit.SECONDS);
    }

    private boolean shouldProtectItem(ItemStack item) {
        if (item.isEmpty()) {
            return false; // Empty slots can be filled with TNT
        }

        // Protect important items
        return item.is(Items.TOTEM_OF_UNDYING) ||           // Totems
               item.is(Items.ELYTRA) ||                     // Elytra
               item.is(Items.NETHERITE_HELMET) ||           // Netherite armor
               item.is(Items.NETHERITE_CHESTPLATE) ||
               item.is(Items.NETHERITE_LEGGINGS) ||
               item.is(Items.NETHERITE_BOOTS) ||
               item.is(Items.DIAMOND_HELMET) ||             // Diamond armor
               item.is(Items.DIAMOND_CHESTPLATE) ||
               item.is(Items.DIAMOND_LEGGINGS) ||
               item.is(Items.DIAMOND_BOOTS) ||
               item.is(Items.NETHERITE_SWORD) ||            // Netherite tools/weapons
               item.is(Items.NETHERITE_PICKAXE) ||
               item.is(Items.NETHERITE_AXE) ||
               item.is(Items.NETHERITE_SHOVEL) ||
               item.is(Items.NETHERITE_HOE) ||
               item.is(Items.DIAMOND_SWORD) ||              // Diamond tools/weapons
               item.is(Items.DIAMOND_PICKAXE) ||
               item.is(Items.DIAMOND_AXE) ||
               item.is(Items.DIAMOND_SHOVEL) ||
               item.is(Items.DIAMOND_HOE) ||
               item.is(Items.ENCHANTED_GOLDEN_APPLE) ||     // God apples
               item.is(Items.GOLDEN_APPLE) ||               // Golden apples
               item.is(Items.ENDER_PEARL) ||                // Ender pearls
               item.is(Items.CHORUS_FRUIT) ||               // Chorus fruit
               item.getEnchantmentTags().size() > 0;        // Any enchanted item
    }
}
