package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class HealthSwapEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "health_swap";
    }
    
    @Override
    public String getName() {
        return "§4§lHealth Swap!";
    }
    
    @Override
    public String getDescription() {
        return "All players' health gets randomly redistributed among them (works across entire server)";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> players = new ArrayList<>(server.getPlayerList().getPlayers());
        if (players.size() < 2) {
            // Show message if not enough players
            for (ServerPlayer player : players) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lNeed at least 2 players for health swap!")));
            }
            return;
        }

        // Show warning message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lHealth is being redistributed among " + players.size() + " players! §7Your health may change!")));
        }

        // Collect all health values
        List<Float> healthValues = new ArrayList<>();
        for (ServerPlayer player : players) {
            healthValues.add(player.getHealth());
        }

        // Shuffle the health values
        Collections.shuffle(healthValues);

        // Redistribute health
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);
            float oldHealth = player.getHealth();
            float newHealth = healthValues.get(i);
            player.setHealth(newHealth);

            // Show individual message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lHealth swapped! §7" + String.format("%.1f", oldHealth) + " → " + String.format("%.1f", newHealth))));
        }
    }
}
