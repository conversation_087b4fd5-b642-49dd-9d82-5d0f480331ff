package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;

public class InstantNightEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "instant_night";
    }
    
    @Override
    public String getName() {
        return "§1§lInstant Night!";
    }
    
    @Override
    public String getDescription() {
        return "Time set to midnight";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerLevel level : server.getAllLevels()) {
            level.setDayTime(18000); // Midnight
        }
    }
}
