package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.EnderMan;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class EndermanInvasionEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final List<EnderMan> spawnedEndermen = new ArrayList<>();
    private static final Map<BlockPos, BlockState> stolenBlocks = new HashMap<>();
    
    @Override
    public String getId() {
        return "enderman_invasion";
    }
    
    @Override
    public String getName() {
        return "§5§lEnderman Invasion!";
    }
    
    @Override
    public String getDescription() {
        return "50+ endermen teleport around, stealing blocks and teleporting players for 3 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        spawnedEndermen.clear();
        stolenBlocks.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§5§lThe void calls... §8Reality tears open!")));
        }
        
        // Wait 3 seconds then start spawning endermen
        scheduler.schedule(() -> {
            // Spawn 50-70 endermen around all players
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();
                
                int endermanCount = 50 + random.nextInt(21); // 50-70 endermen per player
                for (int i = 0; i < endermanCount; i++) {
                    // Spawn endermen in a large area around player
                    int x = playerPos.getX() + random.nextInt(120) - 60; // 120 block radius
                    int z = playerPos.getZ() + random.nextInt(120) - 60;
                    int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
                    
                    BlockPos spawnPos = new BlockPos(x, y, z);
                    
                    // Make sure spawn position is safe
                    if (level.getBlockState(spawnPos).isAir() && 
                        level.getBlockState(spawnPos.above()).isAir() &&
                        level.getBlockState(spawnPos.above().above()).isAir() &&
                        !level.getBlockState(spawnPos.below()).isAir()) {
                        
                        EnderMan enderman = EntityType.ENDERMAN.create(level);
                        if (enderman != null) {
                            enderman.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                            level.addFreshEntity(enderman);
                            spawnedEndermen.add(enderman);
                        }
                    }
                }
            }
            
            // Show invasion started message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§5§lENDERMAN INVASION! §8They're everywhere!")));
            }
            
            // Enderman chaos effects for 3 minutes (every 2 seconds)
            for (int i = 0; i < 90; i++) { // 3 minutes / 2 seconds = 90 iterations
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();
                        
                        // Player teleportation removed - was too annoying
                        
                        // Endermen steal blocks around players (40% chance)
                        if (random.nextInt(10) < 4) {
                            int stealCount = 3 + random.nextInt(5); // 3-7 blocks stolen
                            for (int j = 0; j < stealCount; j++) {
                                int x = playerPos.getX() + random.nextInt(30) - 15;
                                int z = playerPos.getZ() + random.nextInt(30) - 15;
                                int y = playerPos.getY() + random.nextInt(10) - 5;
                                
                                BlockPos stealPos = new BlockPos(x, y, z);
                                
                                if (level.isInWorldBounds(stealPos)) {
                                    BlockState blockState = level.getBlockState(stealPos);
                                    
                                    // Only steal certain block types (like endermen normally do)
                                    if (!blockState.isAir() && 
                                        (blockState.is(Blocks.DIRT) || blockState.is(Blocks.GRASS_BLOCK) || 
                                         blockState.is(Blocks.STONE) || blockState.is(Blocks.SAND) ||
                                         blockState.is(Blocks.GRAVEL) || blockState.is(Blocks.CLAY))) {
                                        
                                        stolenBlocks.put(stealPos, blockState);
                                        level.setBlock(stealPos, Blocks.AIR.defaultBlockState(), 3);
                                        
                                        // Play enderman sound
                                        level.playSound(null, stealPos, SoundEvents.ENDERMAN_AMBIENT, SoundSource.HOSTILE, 0.5f, 1.0f);
                                    }
                                }
                            }
                        }
                        
                        // Spawn additional endermen occasionally (10% chance)
                        if (random.nextInt(10) == 0) {
                            int x = playerPos.getX() + random.nextInt(40) - 20;
                            int z = playerPos.getZ() + random.nextInt(40) - 20;
                            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
                            
                            BlockPos spawnPos = new BlockPos(x, y, z);
                            
                            if (level.getBlockState(spawnPos).isAir() && 
                                level.getBlockState(spawnPos.above()).isAir() &&
                                !level.getBlockState(spawnPos.below()).isAir()) {
                                
                                EnderMan enderman = EntityType.ENDERMAN.create(level);
                                if (enderman != null) {
                                    enderman.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                                    level.addFreshEntity(enderman);
                                    spawnedEndermen.add(enderman);
                                }
                            }
                        }
                    }
                    
                    // Show periodic invasion messages
                    if (random.nextInt(15) == 0) { // ~6.7% chance
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] invasionMessages = {
                                "§5§lENDERMEN EVERYWHERE!",
                                "§8§lTHEY'RE STEALING EVERYTHING!",
                                "§5§lREALITY IS BREAKING!",
                                "§8§lTHE VOID CONSUMES!",
                                "§5§lENDERMAN CHAOS!",
                                "§8§lNOWHERE IS SAFE!",
                                "§5§lTHEY'RE WATCHING YOU!"
                            };
                            String message = invasionMessages[random.nextInt(invasionMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 2L, TimeUnit.SECONDS); // Every 2 seconds
            }
            
            // Clean up after 3 minutes
            scheduler.schedule(() -> {
                // Remove all spawned endermen
                for (EnderMan enderman : spawnedEndermen) {
                    if (enderman.isAlive()) {
                        enderman.discard();
                    }
                }
                spawnedEndermen.clear();
                
                // Restore some stolen blocks (50% chance each)
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    for (Map.Entry<BlockPos, BlockState> entry : stolenBlocks.entrySet()) {
                        if (random.nextBoolean()) { // 50% chance to restore
                            BlockPos pos = entry.getKey();
                            BlockState state = entry.getValue();
                            if (level.getBlockState(pos).isAir()) {
                                level.setBlock(pos, state, 3);
                            }
                        }
                    }
                }
                stolenBlocks.clear();
                
                // Show end message
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe endermen retreat... §5§lBut the void remembers...")));
                }
            }, 180L, TimeUnit.SECONDS); // 3 minutes
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
