package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.phys.Vec3;

import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MagnetPlayersEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "magnet_players";
    }
    
    @Override
    public String getName() {
        return "§c§lMagnet Players!";
    }
    
    @Override
    public String getDescription() {
        return "Players get attracted to each other and stick together for 90 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYou feel a strange magnetic force... §7Players are being drawn together!")));
        }
        
        // Wait 3 seconds then start the magnetic effect
        scheduler.schedule(() -> {
            // Magnetic effects for 90 seconds (every 0.1 seconds for smooth movement)
            for (int i = 0; i < 900; i++) { // 90 seconds / 0.1 = 900 iterations
                scheduler.schedule(() -> {
                    List<ServerPlayer> players = server.getPlayerList().getPlayers();
                    
                    if (players.size() < 2) {
                        return; // Need at least 2 players for magnetic effect
                    }
                    
                    // Apply magnetic forces between all players
                    for (int j = 0; j < players.size(); j++) {
                        ServerPlayer player1 = players.get(j);
                        Vec3 player1Pos = player1.position();
                        Vec3 totalForce = Vec3.ZERO;
                        
                        // Calculate magnetic force from all other players
                        for (int k = 0; k < players.size(); k++) {
                            if (j == k) continue; // Don't attract to self
                            
                            ServerPlayer player2 = players.get(k);
                            Vec3 player2Pos = player2.position();
                            
                            // Calculate distance and direction
                            Vec3 direction = player2Pos.subtract(player1Pos);
                            double distance = direction.length();
                            
                            if (distance > 0.5) { // Only apply force if not too close
                                // Normalize direction and apply magnetic force
                                Vec3 normalizedDirection = direction.normalize();
                                
                                // Magnetic force gets stronger as players get closer (inverse square law)
                                double forceStrength = Math.min(0.3, 2.0 / (distance * distance));
                                
                                // Add this force to total force
                                totalForce = totalForce.add(normalizedDirection.scale(forceStrength));
                            } else {
                                // If too close, add slight repulsion to prevent complete overlap
                                Vec3 repulsion = player1Pos.subtract(player2Pos).normalize().scale(0.1);
                                totalForce = totalForce.add(repulsion);
                            }
                        }
                        
                        // Apply the magnetic force to player movement
                        Vec3 currentMotion = player1.getDeltaMovement();
                        Vec3 newMotion = currentMotion.add(totalForce);
                        
                        // Limit maximum speed to prevent crazy acceleration
                        double maxSpeed = 1.5;
                        if (newMotion.length() > maxSpeed) {
                            newMotion = newMotion.normalize().scale(maxSpeed);
                        }
                        
                        player1.setDeltaMovement(newMotion);
                        
                        // Play magnetic sound occasionally
                        if (random.nextInt(100) == 0) { // 1% chance each tick
                            player1.level().playSound(null, player1.blockPosition(), SoundEvents.BEACON_POWER_SELECT, SoundSource.PLAYERS, 0.3f, 1.5f + random.nextFloat() * 0.5f);
                        }
                    }
                    
                    // Show periodic magnetic messages
                    if (random.nextInt(200) == 0) { // 0.5% chance each tick
                        for (ServerPlayer player : players) {
                            String[] magnetMessages = {
                                "§c§lMAGNETIC ATTRACTION!",
                                "§6§lPLAYERS DRAWN TOGETHER!",
                                "§c§lIRRESISTIBLE FORCE!",
                                "§6§lCAN'T ESCAPE THE PULL!",
                                "§c§lMAGNETIC CHAOS!",
                                "§6§lSTUCK LIKE MAGNETS!",
                                "§c§lFORCE FIELD ACTIVE!"
                            };
                            String message = magnetMessages[random.nextInt(magnetMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 100L, TimeUnit.MILLISECONDS); // Every 0.1 seconds
            }
            
            // Show end message after 90 seconds
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe magnetic field fades... §cYou can move freely again!")));
                    
                    // Reset player motion to prevent lingering effects
                    player.setDeltaMovement(Vec3.ZERO);
                }
            }, 90L, TimeUnit.SECONDS);
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
}
