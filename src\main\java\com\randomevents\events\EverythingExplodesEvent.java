package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class EverythingExplodesEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "everything_explodes";
    }
    
    @Override
    public String getName() {
        return "§c§lEverything Explodes!";
    }
    
    @Override
    public String getDescription() {
        return "Random harmless explosions everywhere for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lBOOM! BOOM! BOOM! §7Everything is exploding!")));
        }
        
        // Create explosions for 45 seconds
        for (int i = 0; i < 45; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();
                    
                    // Create 3-6 random explosions around each player
                    int explosionCount = 3 + random.nextInt(4);
                    for (int j = 0; j < explosionCount; j++) {
                        int x = playerPos.getX() + random.nextInt(50) - 25;
                        int y = playerPos.getY() + random.nextInt(20) - 5;
                        int z = playerPos.getZ() + random.nextInt(50) - 25;
                        
                        BlockPos explosionPos = new BlockPos(x, y, z);
                        
                        if (level.isInWorldBounds(explosionPos)) {
                            // Create harmless explosion (no block damage, no entity damage)
                            level.explode(
                                null, // No source entity
                                explosionPos.getX(), 
                                explosionPos.getY(), 
                                explosionPos.getZ(),
                                2.0f, // Explosion power (visual only)
                                false, // No fire
                                net.minecraft.world.level.Level.ExplosionInteraction.NONE // No block damage
                            );
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
    }
}
