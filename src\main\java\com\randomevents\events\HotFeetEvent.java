package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HotFeetEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, Vec3> lastPlayerPositions = new HashMap<>();
    private static final Map<ServerPlayer, Long> stillStartTimes = new HashMap<>();
    private static final Map<BlockPos, BlockState> originalBlocks = new HashMap<>();
    
    @Override
    public String getId() {
        return "hot_feet";
    }
    
    @Override
    public String getName() {
        return "§c§lHot Feet!";
    }
    
    @Override
    public String getDescription() {
        return "Standing still makes the ground under you turn into lava for 45 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        lastPlayerPositions.clear();
        stillStartTimes.clear();
        originalBlocks.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lThe ground is heating up... §6Keep moving or burn!")));
        }
        
        // Wait 3 seconds then start the hot feet effect
        scheduler.schedule(() -> {
            // Hot feet monitoring for 45 seconds (every 0.5 seconds)
            for (int i = 0; i < 90; i++) { // 45 seconds / 0.5 = 90 iterations
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        Vec3 currentPos = player.position();
                        BlockPos playerBlockPos = player.blockPosition();
                        
                        // Check if player has moved significantly (more than 0.5 blocks)
                        Vec3 lastPos = lastPlayerPositions.get(player);
                        boolean hasMovedSignificantly = false;
                        
                        if (lastPos != null) {
                            double distance = currentPos.distanceTo(lastPos);
                            hasMovedSignificantly = distance > 0.5;
                        }
                        
                        if (hasMovedSignificantly || lastPos == null) {
                            // Player is moving, reset still timer
                            stillStartTimes.remove(player);
                            lastPlayerPositions.put(player, currentPos);
                        } else {
                            // Player is standing still
                            long currentTime = System.currentTimeMillis();
                            
                            if (!stillStartTimes.containsKey(player)) {
                                // Just started standing still
                                stillStartTimes.put(player, currentTime);
                            } else {
                                // Check how long they've been still
                                long stillDuration = currentTime - stillStartTimes.get(player);
                                
                                if (stillDuration >= 2000) { // 2 seconds of standing still
                                    // Turn ground under player into lava
                                    turnGroundToLava(player, level, playerBlockPos);
                                    
                                    // Reset timer to prevent constant lava placement
                                    stillStartTimes.put(player, currentTime);
                                }
                            }
                        }
                        
                        // Update last position
                        lastPlayerPositions.put(player, currentPos);
                    }
                    
                    // Show periodic warning messages
                    if (random.nextInt(20) == 0) { // 5% chance each check
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            String[] hotFeetMessages = {
                                "§c§lKEEP MOVING!",
                                "§6§lTHE GROUND BURNS!",
                                "§c§lHOT FEET ACTIVE!",
                                "§6§lDON'T STAND STILL!",
                                "§c§lLAVA BENEATH YOU!",
                                "§6§lMOVE OR BURN!",
                                "§c§lTHE FLOOR IS LAVA!"
                            };
                            String message = hotFeetMessages[random.nextInt(hotFeetMessages.length)];
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal(message)));
                        }
                    }
                }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
            }
            
            // Clean up after 45 seconds
            scheduler.schedule(() -> {
                // Restore original blocks
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    for (Map.Entry<BlockPos, BlockState> entry : originalBlocks.entrySet()) {
                        BlockPos pos = entry.getKey();
                        BlockState originalState = entry.getValue();
                        
                        // Only restore if it's still lava
                        if (level.getBlockState(pos).is(Blocks.LAVA)) {
                            level.setBlock(pos, originalState, 3);
                        }
                    }
                }
                
                // Clear tracking data
                lastPlayerPositions.clear();
                stillStartTimes.clear();
                originalBlocks.clear();
                
                // Show end message
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lThe ground cools down... §aYou can rest your feet now.")));
                }
            }, 45L, TimeUnit.SECONDS);
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }
    
    private void turnGroundToLava(ServerPlayer player, ServerLevel level, BlockPos playerPos) {
        // Turn blocks in a 3x3 area under the player into lava
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos targetPos = playerPos.offset(x, -1, z); // One block below player
                
                if (level.isInWorldBounds(targetPos)) {
                    BlockState currentState = level.getBlockState(targetPos);
                    
                    // Don't replace air, lava, or important blocks
                    if (!currentState.isAir() && 
                        !currentState.is(Blocks.LAVA) &&
                        !currentState.is(Blocks.BEDROCK) &&
                        !currentState.is(Blocks.OBSIDIAN)) {
                        
                        // Store original block for restoration
                        if (!originalBlocks.containsKey(targetPos)) {
                            originalBlocks.put(targetPos, currentState);
                        }
                        
                        // Replace with lava
                        level.setBlock(targetPos, Blocks.LAVA.defaultBlockState(), 3);
                        
                        // Play lava sound
                        if (random.nextInt(3) == 0) { // 33% chance for sound
                            level.playSound(null, targetPos, SoundEvents.LAVA_POP, SoundSource.BLOCKS, 0.5f, 1.0f + random.nextFloat() * 0.5f);
                        }
                    }
                }
            }
        }
        
        // Show warning to player
        if (random.nextInt(4) == 0) { // 25% chance for personal warning
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYour feet are burning the ground! §6MOVE!")));
        }
        
        // Play warning sound to player
        level.playSound(null, playerPos, SoundEvents.FIRE_AMBIENT, SoundSource.PLAYERS, 0.3f, 1.5f);
    }
}
