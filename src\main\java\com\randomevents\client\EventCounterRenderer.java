package com.randomevents.client;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = "randomevents", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class EventCounterRenderer {
    
    private static String counterText = "";
    private static boolean showCounter = false;
    
    public static void setCounterText(String text) {
        counterText = text;
        showCounter = !text.isEmpty();
    }
    
    public static void hideCounter() {
        showCounter = false;
        counterText = "";
    }
    
    @SubscribeEvent
    public static void onRenderGuiOverlay(RenderGuiOverlayEvent.Post event) {
        if (!showCounter || counterText.isEmpty()) {
            return;
        }
        
        Minecraft minecraft = Minecraft.getInstance();
        if (minecraft.player == null || minecraft.level == null) {
            return;
        }
        
        GuiGraphics guiGraphics = event.getGuiGraphics();
        Font font = minecraft.font;
        
        // Get screen dimensions
        int screenWidth = minecraft.getWindow().getGuiScaledWidth();
        
        // Calculate text width for centering
        int textWidth = font.width(counterText);
        int x = (screenWidth - textWidth) / 2; // Center horizontally
        int y = 5; // 5 pixels from the top
        
        // Draw text with black outline and white fill
        // First draw black outline (offset in all directions)
        guiGraphics.drawString(font, counterText, x - 1, y - 1, 0x000000, false); // Top-left
        guiGraphics.drawString(font, counterText, x + 1, y - 1, 0x000000, false); // Top-right
        guiGraphics.drawString(font, counterText, x - 1, y + 1, 0x000000, false); // Bottom-left
        guiGraphics.drawString(font, counterText, x + 1, y + 1, 0x000000, false); // Bottom-right
        guiGraphics.drawString(font, counterText, x - 1, y, 0x000000, false);     // Left
        guiGraphics.drawString(font, counterText, x + 1, y, 0x000000, false);     // Right
        guiGraphics.drawString(font, counterText, x, y - 1, 0x000000, false);     // Top
        guiGraphics.drawString(font, counterText, x, y + 1, 0x000000, false);     // Bottom
        
        // Then draw white text on top
        guiGraphics.drawString(font, counterText, x, y, 0xFFFFFF, false);
    }
}
