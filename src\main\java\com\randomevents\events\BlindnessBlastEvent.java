package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class BlindnessBlastEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "blindness_blast";
    }
    
    @Override
    public String getName() {
        return "§0§lBlindness Blast!";
    }
    
    @Override
    public String getDescription() {
        return "All players blinded with terrifying fake explosions for 20 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Apply blindness to all players
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.addEffect(new MobEffectInstance(MobEffects.BLINDNESS, 20 * 20, 0)); // 20 seconds

            // Show scary message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§0§lEVERYTHING IS DARK! §7You hear explosions everywhere!")));
        }

        // Create terrifying fake explosions around players for 20 seconds
        for (int i = 0; i < 20; i++) { // Every second for 20 seconds
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Create 5-8 fake explosions around each player
                    int explosionCount = 5 + random.nextInt(4);
                    for (int j = 0; j < explosionCount; j++) {
                        // Random position around player (within 15 blocks)
                        int x = playerPos.getX() + random.nextInt(30) - 15;
                        int y = playerPos.getY() + random.nextInt(10) - 2;
                        int z = playerPos.getZ() + random.nextInt(30) - 15;

                        // Create FAKE explosion sounds and effects (NO DAMAGE!)
                        // Play explosion sound
                        level.playSound(null, x, y, z, SoundEvents.GENERIC_EXPLODE,
                                      SoundSource.HOSTILE, 1.0f, 0.8f + random.nextFloat() * 0.4f);

                        // Play additional scary sounds
                        if (random.nextInt(3) == 0) {
                            level.playSound(null, x, y, z, SoundEvents.TNT_PRIMED,
                                          SoundSource.HOSTILE, 0.8f, 1.0f);
                        }

                        // No actual explosion - just scary sounds!
                    }

                    // Occasionally show scary messages
                    if (random.nextInt(3) == 0) { // 33% chance
                        String[] scaryMessages = {
                            "§c§lBOOM! §7Something exploded nearby!",
                            "§4§lCRASH! §7You hear destruction!",
                            "§c§lBANG! §7Explosions surround you!",
                            "§4§lKABOOM! §7The world is ending!",
                            "§c§lBLAST! §7You're under attack!"
                        };
                        String message = scaryMessages[random.nextInt(scaryMessages.length)];
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal(message)));
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
    }
}
