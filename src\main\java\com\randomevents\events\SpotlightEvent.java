package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SpotlightEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private final List<BlockPos> lightBlocks = new ArrayList<>();
    
    @Override
    public String getId() {
        return "spotlight";
    }
    
    @Override
    public String getName() {
        return "§e§lSpotlight!";
    }
    
    @Override
    public String getDescription() {
        return "One player is constantly followed by a bright light for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> players = new ArrayList<>(server.getPlayerList().getPlayers());
        if (players.isEmpty()) return;
        
        // Choose random player to spotlight
        ServerPlayer spotlightPlayer = players.get(random.nextInt(players.size()));
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (player == spotlightPlayer) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§lYou are in the SPOTLIGHT! §7Everyone can see you!")));
            } else {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§l" + spotlightPlayer.getName().getString() + " is in the spotlight! §7Follow the light!")));
            }
        }
        
        // Follow the player with light for 2 minutes
        for (int i = 0; i < 120; i++) { // 2 minutes
            scheduler.schedule(() -> {
                if (spotlightPlayer.isAlive() && spotlightPlayer.getServer() != null) {
                    ServerLevel level = spotlightPlayer.serverLevel();
                    BlockPos playerPos = spotlightPlayer.blockPosition();
                    
                    // Clear old light blocks
                    for (BlockPos oldLight : lightBlocks) {
                        if (level.getBlockState(oldLight).is(Blocks.LIGHT)) {
                            level.setBlock(oldLight, Blocks.AIR.defaultBlockState(), 3);
                        }
                    }
                    lightBlocks.clear();
                    
                    // Create new light around player
                    for (int x = -3; x <= 3; x++) {
                        for (int z = -3; z <= 3; z++) {
                            for (int y = 2; y <= 5; y++) { // Above player
                                BlockPos lightPos = playerPos.offset(x, y, z);
                                
                                // Create circular pattern
                                if (x * x + z * z <= 9 && level.isInWorldBounds(lightPos) && 
                                    level.getBlockState(lightPos).isAir()) {
                                    level.setBlock(lightPos, Blocks.LIGHT.defaultBlockState(), 3);
                                    lightBlocks.add(lightPos);
                                }
                            }
                        }
                    }
                    
                    // Also give glowing effect to the player
                    spotlightPlayer.addEffect(new net.minecraft.world.effect.MobEffectInstance(
                        net.minecraft.world.effect.MobEffects.GLOWING, 40, 0)); // 2 seconds
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
        
        // Clean up all light blocks after 2 minutes
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                for (BlockPos lightPos : lightBlocks) {
                    if (level.getBlockState(lightPos).is(Blocks.LIGHT)) {
                        level.setBlock(lightPos, Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
            lightBlocks.clear();
            
            // Final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7The spotlight fades away...")));
            }
        }, 120L, TimeUnit.SECONDS);
    }
}
