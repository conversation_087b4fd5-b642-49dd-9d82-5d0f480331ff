package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ItemRainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Array of random items to rain down
    private static final net.minecraft.world.item.Item[] RAIN_ITEMS = {
        Items.STICK, Items.STONE, Items.DIRT, Items.COBBLESTONE, Items.OAK_PLANKS,
        Items.APPLE, Items.BREAD, Items.CARROT, Items.POTATO, Items.WHEAT,
        Items.IRON_INGOT, Items.GOLD_INGOT, Items.DIAMOND, Items.EMERALD, Items.COAL,
        Items.ARROW, Items.BOW, Items.FISHING_ROD, Items.BUCKET, Items.SHEARS,
        Items.LEATHER, Items.FEATHER, Items.STRING, Items.BONE, Items.GUNPOWDER,
        Items.REDSTONE, Items.GLOWSTONE_DUST, Items.ENDER_PEARL, Items.BLAZE_ROD, Items.GHAST_TEAR
    };
    
    @Override
    public String getId() {
        return "item_rain";
    }
    
    @Override
    public String getName() {
        return "§e§lItem Rain!";
    }
    
    @Override
    public String getDescription() {
        return "MASSIVE amounts of random items fall from the sky for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lThe sky opens up... §7MASSIVE item storm incoming!")));
        }

        // Rain items for 2 minutes (every 0.5 seconds for more intensity)
        for (int i = 0; i < 240; i++) { // 2 minutes * 2 drops per second = 240 drops
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Drop 20-40 random items around each player (MASSIVE increase!)
                    int itemCount = 20 + random.nextInt(21);
                    for (int j = 0; j < itemCount; j++) {
                        int x = playerPos.getX() + random.nextInt(80) - 40; // Larger area
                        int z = playerPos.getZ() + random.nextInt(80) - 40;
                        int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 20 + random.nextInt(15);

                        BlockPos dropPos = new BlockPos(x, y, z);

                        // Choose random item
                        net.minecraft.world.item.Item randomItem = RAIN_ITEMS[random.nextInt(RAIN_ITEMS.length)];
                        ItemStack itemStack = new ItemStack(randomItem, 1 + random.nextInt(5)); // 1-5 items

                        // Drop the item
                        if (level.isInWorldBounds(dropPos)) {
                            net.minecraft.world.entity.item.ItemEntity itemEntity =
                                new net.minecraft.world.entity.item.ItemEntity(level,
                                    dropPos.getX() + 0.5, dropPos.getY(), dropPos.getZ() + 0.5, itemStack);

                            // Make items fall faster
                            itemEntity.setDeltaMovement(0, -0.8, 0);
                            level.addFreshEntity(itemEntity);
                        }
                    }
                }

                // Show periodic messages
                if (random.nextInt(10) == 0) { // 10% chance each drop
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        String[] rainMessages = {
                            "§e§lItems raining down everywhere!",
                            "§6§lThe sky is full of loot!",
                            "§e§lMassive item storm!",
                            "§6§lItems falling like rain!",
                            "§e§lLoot apocalypse!"
                        };
                        String message = rainMessages[random.nextInt(rainMessages.length)];
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal(message)));
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
        }
    }
}
