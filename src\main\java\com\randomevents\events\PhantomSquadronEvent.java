package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.Phantom;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class PhantomSquadronEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "phantom_squadron";
    }
    
    @Override
    public String getName() {
        return "§9§lPhantom Squadron!";
    }
    
    @Override
    public String getDescription() {
        return "15 phantoms spawn during day and dive bomb players for 3 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<Phantom> spawnedPhantoms = new ArrayList<>();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§9§lThe sky darkens with wings... §7Look up!")));
        }
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Spawn 15 phantoms around each player
            for (int i = 0; i < 15; i++) {
                int x = playerPos.getX() + random.nextInt(60) - 30;
                int z = playerPos.getZ() + random.nextInt(60) - 30;
                int y = playerPos.getY() + 20 + random.nextInt(30); // High in the sky
                
                BlockPos spawnPos = new BlockPos(x, y, z);
                
                // Make sure the spawn position is in bounds and in air
                if (level.isInWorldBounds(spawnPos) && level.getBlockState(spawnPos).isAir()) {
                    Phantom phantom = EntityType.PHANTOM.create(level);
                    if (phantom != null) {
                        phantom.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                        
                        // Make phantoms more aggressive
                        phantom.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.MOVEMENT_SPEED)
                            .setBaseValue(1.5); // Faster than normal
                        phantom.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.ATTACK_DAMAGE)
                            .setBaseValue(4.0); // More damage
                        
                        level.addFreshEntity(phantom);
                        spawnedPhantoms.add(phantom);
                    }
                }
            }
        }
        
        // Make phantoms more aggressive by constantly targeting players
        for (int i = 0; i < 90; i++) { // Every 2 seconds for 3 minutes
            scheduler.schedule(() -> {
                for (Phantom phantom : spawnedPhantoms) {
                    if (phantom.isAlive()) {
                        ServerLevel level = (ServerLevel) phantom.level();
                        
                        // Find nearest player
                        ServerPlayer nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;
                        
                        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
                            if (player.serverLevel() == level) {
                                double distance = phantom.distanceToSqr(player);
                                if (distance < nearestDistance) {
                                    nearestDistance = distance;
                                    nearestPlayer = player;
                                }
                            }
                        }
                        
                        // Aggressively target the nearest player
                        if (nearestPlayer != null && nearestDistance < 2500) { // Within 50 blocks
                            phantom.setTarget(nearestPlayer);
                        }
                    }
                }
            }, i * 2L, TimeUnit.SECONDS);
        }
        
        // Remove all phantoms after 3 minutes
        scheduler.schedule(() -> {
            for (Phantom phantom : spawnedPhantoms) {
                if (phantom.isAlive()) {
                    phantom.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                }
            }
        }, 180L, TimeUnit.SECONDS);
    }
}
