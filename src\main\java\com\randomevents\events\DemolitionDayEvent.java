package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class DemolitionDayEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    
    @Override
    public String getId() {
        return "demolition_day";
    }
    
    @Override
    public String getName() {
        return "§c§lDemolition Day!";
    }
    
    @Override
    public String getDescription() {
        return "Random structures around players get demolished for 90 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lDEMOLITION DAY! §7Structures are being torn down!")));
        }
        
        // Demolish structures for 90 seconds
        for (int i = 0; i < 90; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();
                    
                    // Find and demolish 10-15 structures per second (VERY aggressive!)
                    int demolitionCount = 10 + ThreadLocalRandom.current().nextInt(6);
                    for (int j = 0; j < demolitionCount; j++) {
                        // Look for structures in a 100 block radius
                        int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(200) - 100;
                        int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(200) - 100;

                        // Check multiple Y levels for structures
                        for (int yOffset = -10; yOffset <= 20; yOffset++) {
                            int y = playerPos.getY() + yOffset;
                            BlockPos demolitionCenter = new BlockPos(x, y, z);

                            // Check if there's a structure here (look for man-made blocks)
                            if (level.isInWorldBounds(demolitionCenter) && isStructureBlock(level, demolitionCenter)) {
                                demolishArea(level, demolitionCenter);

                                // Create explosion effect (no damage)
                                level.explode(null, x, y, z, 2.0f, false,
                                            net.minecraft.world.level.Level.ExplosionInteraction.NONE);
                                break; // Found structure, move to next location
                            }
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
    }
    
    private boolean isStructureBlock(ServerLevel level, BlockPos pos) {
        // Check if this looks like a player-built structure
        return level.getBlockState(pos).is(Blocks.STONE_BRICKS) ||
               level.getBlockState(pos).is(Blocks.COBBLESTONE) ||
               level.getBlockState(pos).is(Blocks.OAK_PLANKS) ||
               level.getBlockState(pos).is(Blocks.BIRCH_PLANKS) ||
               level.getBlockState(pos).is(Blocks.SPRUCE_PLANKS) ||
               level.getBlockState(pos).is(Blocks.JUNGLE_PLANKS) ||
               level.getBlockState(pos).is(Blocks.BRICKS) ||
               level.getBlockState(pos).is(Blocks.SMOOTH_STONE) ||
               level.getBlockState(pos).is(Blocks.POLISHED_GRANITE) ||
               level.getBlockState(pos).is(Blocks.POLISHED_DIORITE) ||
               level.getBlockState(pos).is(Blocks.POLISHED_ANDESITE);
    }
    
    private void demolishArea(ServerLevel level, BlockPos center) {
        // Demolish a 5x5x5 area
        for (int x = -2; x <= 2; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos demolishPos = center.offset(x, y, z);
                    
                    if (level.isInWorldBounds(demolishPos) && 
                        !level.getBlockState(demolishPos).is(Blocks.BEDROCK) &&
                        !level.getBlockState(demolishPos).isAir()) {
                        
                        // Only demolish if it's a structure block
                        if (isStructureBlock(level, demolishPos)) {
                            // 90% chance to demolish each block (aggressive destruction)
                            if (ThreadLocalRandom.current().nextInt(10) < 9) {
                                level.setBlock(demolishPos, Blocks.AIR.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }
        }
    }
}
