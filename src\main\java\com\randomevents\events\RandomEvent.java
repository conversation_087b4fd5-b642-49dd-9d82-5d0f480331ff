package com.randomevents.events;

import net.minecraft.server.MinecraftServer;

public abstract class RandomEvent {
    
    /**
     * Get the unique identifier for this event
     */
    public abstract String getId();
    
    /**
     * Get the display name for this event
     */
    public abstract String getName();
    
    /**
     * Execute the event on the server
     */
    public abstract void execute(MinecraftServer server);
    
    /**
     * Get a description of what this event does
     */
    public abstract String getDescription();

    /**
     * Check if this event can trigger under current conditions
     * Override this method in events that have specific trigger requirements
     */
    public boolean canTrigger(MinecraftServer server) {
        return true; // Default: all events can trigger
    }
}
