package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class BedrockHoleEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "bedrock_hole";
    }
    
    @Override
    public String getName() {
        return "§8§lBedrock Hole!";
    }
    
    @Override
    public String getDescription() {
        return "Creates a hole under each player down to bedrock after a 5 second warning";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // First, warn all players with actionbar text (smaller)
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                Component.literal("§c§lYou better start jumping... §7The ground is about to collapse!")));
        }

        // After 5 seconds, create the holes
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Create a 5x5 hole centered on the player
                for (int x = -2; x <= 2; x++) {
                    for (int z = -2; z <= 2; z++) {
                        // Create hole from current Y level down to bedrock
                        for (int y = playerPos.getY(); y >= level.getMinBuildHeight() + 1; y--) {
                            BlockPos holePos = new BlockPos(playerPos.getX() + x, y, playerPos.getZ() + z);

                            if (level.isInWorldBounds(holePos)) {
                                // Don't destroy bedrock
                                if (!level.getBlockState(holePos).is(Blocks.BEDROCK)) {
                                    level.setBlock(holePos, Blocks.AIR.defaultBlockState(), 3);
                                }
                            }
                        }
                    }
                }

                // Send actionbar when the hole appears
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    Component.literal("§8§lThe ground gives way beneath you!")));
            }
        }, 5L, TimeUnit.SECONDS);
    }
}
