package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ColorMatchPuzzleEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    // Color blocks for the puzzle (smaller selection for 3x3)
    private static final Block[] COLOR_BLOCKS = {
        Blocks.RED_WOOL, Blocks.BLUE_WOOL, Blocks.GREEN_WOOL, Blocks.YELLOW_WOOL
    };

    private static final Map<BlockPos, BlockState> originalBlocks = new HashMap<>();
    private static final Map<BlockPos, Block> targetPattern = new HashMap<>();
    private static BlockPos puzzleCenter;
    private static BlockPos playerPlatform;
    private static boolean puzzleActive = false;
    
    @Override
    public String getId() {
        return "color_match_puzzle";
    }
    
    @Override
    public String getName() {
        return "§e§lColor Match Puzzle!";
    }
    
    @Override
    public String getDescription() {
        return "Work together to recreate the color pattern or everyone explodes!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        originalBlocks.clear();
        targetPattern.clear();
        puzzleActive = true;

        // Pause the main event timer (will resume when puzzle ends)
        // TODO: Add pause functionality to RandomEventManager

        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        if (players.isEmpty()) return;

        ServerLevel level = server.overworld();

        // Create puzzle high in the sky (150 blocks up)
        ServerPlayer firstPlayer = players.get(0);
        BlockPos basePos = firstPlayer.blockPosition();
        int skyY = Math.max(150, level.getMaxBuildHeight() - 50);

        // Puzzle center in the sky
        puzzleCenter = new BlockPos(basePos.getX(), skyY, basePos.getZ());

        // Player platform 10 blocks below puzzle
        playerPlatform = puzzleCenter.below(10);

        // Create sky platforms
        createSkyPlatforms(level);

        // Teleport all players to the platform in the sky
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);
            double x = playerPlatform.getX() + (i - players.size() / 2.0) * 2; // Spread players out
            double z = playerPlatform.getZ();

            player.teleportTo(level, x + 0.5, playerPlatform.getY() + 1, z + 0.5, 0, 0);

            // Give players the blocks they need
            givePlayerBlocks(player);

            // Play teleport sound
            level.playSound(null, playerPlatform, SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
        }

        // Show cooperation message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lLook up! Work together to recreate the pattern!")));
        }

        // Wait 3 seconds then start the puzzle
        scheduler.schedule(() -> {
            createPuzzleGrid(level);
            showTargetPattern(server);
        }, 3L, TimeUnit.SECONDS);
    }
    
    private void createSkyPlatforms(ServerLevel level) {
        // Create player platform (7x7 obsidian platform)
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                BlockPos pos = playerPlatform.offset(x, 0, z);
                level.setBlock(pos, Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }

        // Create puzzle platform above (3x3 for the puzzle)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = puzzleCenter.offset(x, 0, z);
                level.setBlock(pos, Blocks.STONE.defaultBlockState(), 3);
            }
        }

        // Create connecting pillars
        for (int y = 1; y < 10; y++) {
            level.setBlock(playerPlatform.above(y), Blocks.OBSIDIAN.defaultBlockState(), 3);
        }
    }

    private void givePlayerBlocks(ServerPlayer player) {
        // Give players the blocks they need for the 3x3 puzzle
        for (Block colorBlock : COLOR_BLOCKS) {
            player.getInventory().add(new net.minecraft.world.item.ItemStack(colorBlock.asItem(), 9)); // 9 blocks of each color
        }

        // Show message about blocks
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§a§lYou received colored blocks! Use them to recreate the pattern!")));
    }

    private void createPuzzleGrid(ServerLevel level) {
        // Create a 3x3 puzzle grid (much smaller)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = puzzleCenter.offset(x, 1, z); // One block above the platform

                // Store original block (air)
                originalBlocks.put(pos, level.getBlockState(pos));

                // Start with white wool
                level.setBlock(pos, Blocks.WHITE_WOOL.defaultBlockState(), 3);
            }
        }

        // Generate random target pattern (3x3 grid)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = puzzleCenter.offset(x, 1, z);
                Block randomColor = COLOR_BLOCKS[random.nextInt(COLOR_BLOCKS.length)];
                targetPattern.put(pos, randomColor);
            }
        }
    }
    

    
    private void showTargetPattern(MinecraftServer server) {
        ServerLevel level = server.overworld();

        // Show the target pattern for 15 seconds
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lMEMORIZE THIS PATTERN! §7You have 15 seconds...")));
        }

        // Display the pattern
        for (Map.Entry<BlockPos, Block> entry : targetPattern.entrySet()) {
            BlockPos pos = entry.getKey();
            Block targetBlock = entry.getValue();
            level.setBlock(pos, targetBlock.defaultBlockState(), 3);
        }

        // Play pattern reveal sound
        level.playSound(null, puzzleCenter, SoundEvents.ANVIL_LAND, SoundSource.BLOCKS, 2.0f, 1.0f);

        // Show countdown during memorization
        for (int i = 1; i <= 3; i++) {
            final int timeLeft = 15 - (i * 5);
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§e§l" + timeLeft + " seconds left to memorize!")));
                }
            }, i * 5L, TimeUnit.SECONDS);
        }

        // Hide pattern after 15 seconds and start the challenge
        scheduler.schedule(() -> {
            // Reset to white wool
            for (BlockPos pos : targetPattern.keySet()) {
                level.setBlock(pos, Blocks.WHITE_WOOL.defaultBlockState(), 3);
            }

            // Start the challenge
            startChallenge(server);

        }, 15L, TimeUnit.SECONDS);
    }
    
    private void startChallenge(MinecraftServer server) {
        // Show challenge message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lRECREATE THE PATTERN! §7You have 20 seconds or BOOM!")));
        }
        
        // Play urgent sound
        ServerLevel level = server.overworld();
        level.playSound(null, puzzleCenter, SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.BLOCKS, 2.0f, 2.0f);
        
        // Start countdown warnings
        for (int i = 1; i <= 4; i++) {
            final int timeLeft = 20 - (i * 5);
            scheduler.schedule(() -> {
                if (puzzleActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§c§l" + timeLeft + " SECONDS LEFT! §7Complete the pattern!")));
                    }
                    level.playSound(null, puzzleCenter, SoundEvents.GENERIC_EXPLODE, SoundSource.BLOCKS, 1.0f, 0.5f);
                }
            }, i * 5L, TimeUnit.SECONDS);
        }
        
        // Check solution every 2 seconds
        for (int i = 1; i <= 10; i++) { // 10 checks over 20 seconds
            scheduler.schedule(() -> {
                if (puzzleActive) {
                    checkSolution(server);
                }
            }, i * 2L, TimeUnit.SECONDS);
        }
        
        // Final explosion if not solved in 20 seconds
        scheduler.schedule(() -> {
            if (puzzleActive) {
                explodeEveryone(server);
            }
        }, 20L, TimeUnit.SECONDS);
    }
    
    private void checkSolution(MinecraftServer server) {
        ServerLevel level = server.overworld();
        boolean isCorrect = true;
        
        // Check if current pattern matches target pattern
        for (Map.Entry<BlockPos, Block> entry : targetPattern.entrySet()) {
            BlockPos pos = entry.getKey();
            Block targetBlock = entry.getValue();
            BlockState currentState = level.getBlockState(pos);
            
            if (currentState.getBlock() != targetBlock) {
                isCorrect = false;
                break;
            }
        }
        
        if (isCorrect) {
            // Success!
            puzzleActive = false;
            
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lPUZZLE SOLVED! §7Excellent teamwork!")));
            }
            
            // Play success sound
            level.playSound(null, puzzleCenter, SoundEvents.PLAYER_LEVELUP, SoundSource.BLOCKS, 2.0f, 1.0f);
            
            // Clean up after 5 seconds
            scheduler.schedule(() -> {
                cleanupPuzzle(level);
            }, 5L, TimeUnit.SECONDS);
        }
    }
    
    private void explodeEveryone(MinecraftServer server) {
        puzzleActive = false;
        
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lTIME'S UP! §4§lTEAMWORK FAILED!")));
            
            // Create explosion at each player's location
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Visual explosion (no block damage)
            level.explode(null, playerPos.getX(), playerPos.getY(), playerPos.getZ(), 
                         3.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
            
            // Deal 3 hearts of damage
            player.hurt(level.damageSources().explosion(null, null), 6.0f);
        }
        
        // Clean up
        scheduler.schedule(() -> {
            cleanupPuzzle(server.overworld());
        }, 3L, TimeUnit.SECONDS);
    }
    
    private void cleanupPuzzle(ServerLevel level) {
        // Resume the main event timer
        // TODO: Add resume functionality to RandomEventManager

        // Remove puzzle blocks
        for (BlockPos pos : targetPattern.keySet()) {
            level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
        }

        // Remove sky platforms
        // Remove player platform
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                BlockPos pos = playerPlatform.offset(x, 0, z);
                level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Remove puzzle platform
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = puzzleCenter.offset(x, 0, z);
                level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Remove connecting pillars
        for (int y = 1; y < 10; y++) {
            level.setBlock(playerPlatform.above(y), Blocks.AIR.defaultBlockState(), 3);
        }

        originalBlocks.clear();
        targetPattern.clear();
    }
}
