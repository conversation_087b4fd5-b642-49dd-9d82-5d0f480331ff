package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DancePartyEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static boolean danceActive = false;
    
    @Override
    public String getId() {
        return "dance_party";
    }
    
    @Override
    public String getName() {
        return "§d§lDance Party!";
    }
    
    @Override
    public String getDescription() {
        return "Players randomly crouch/jump uncontrollably for 1 minute";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        danceActive = true;
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§d§l♪ ♫ Dance time! ♫ ♪ §7You can't stop moving!")));
        }
        
        // Register event listener
        MinecraftForge.EVENT_BUS.register(new DanceEventHandler());
        
        // Force dancing movements every few ticks
        for (int i = 0; i < 1200; i++) { // 1 minute worth of ticks (60 seconds * 20 ticks)
            scheduler.schedule(() -> {
                if (danceActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        // Random chance for different dance moves
                        int danceMove = random.nextInt(10);
                        
                        switch (danceMove) {
                            case 0, 1, 2: // 30% chance - Force crouch
                                player.setShiftKeyDown(true);
                                // Stop crouching after a short time
                                scheduler.schedule(() -> player.setShiftKeyDown(false), 200L, TimeUnit.MILLISECONDS);
                                break;
                                
                            case 3, 4: // 20% chance - Force jump
                                if (player.onGround()) {
                                    player.jumpFromGround();
                                }
                                break;
                                
                            case 5: // 10% chance - Spin around
                                player.setYRot(player.getYRot() + 45 + random.nextInt(90));
                                break;
                                
                            case 6: // 10% chance - Quick crouch-uncrouch
                                player.setShiftKeyDown(true);
                                scheduler.schedule(() -> {
                                    player.setShiftKeyDown(false);
                                    scheduler.schedule(() -> player.setShiftKeyDown(true), 100L, TimeUnit.MILLISECONDS);
                                    scheduler.schedule(() -> player.setShiftKeyDown(false), 200L, TimeUnit.MILLISECONDS);
                                }, 100L, TimeUnit.MILLISECONDS);
                                break;
                                
                            case 7: // 10% chance - Look up and down rapidly
                                player.setXRot(random.nextFloat() * 60 - 30); // Random pitch between -30 and 30
                                break;
                                
                            // Cases 8 and 9 (20%) - Do nothing this tick
                        }
                    }
                }
            }, i * 50L, TimeUnit.MILLISECONDS); // Every 50ms (1 tick)
        }
        
        // Stop dance effect after 1 minute
        scheduler.schedule(() -> {
            danceActive = false;
            MinecraftForge.EVENT_BUS.unregister(DanceEventHandler.class);

            // Make sure all players stop crouching
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.setShiftKeyDown(false);
            }
        }, 60L, TimeUnit.SECONDS);
    }
    
    public static class DanceEventHandler {
        @SubscribeEvent
        public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!danceActive || !(event.getEntity() instanceof ServerPlayer player)) return;
            
            // Add some random movement while dancing
            if (random.nextInt(20) == 0) { // 5% chance per tick
                // Small random movement
                double randomX = (random.nextDouble() - 0.5) * 0.1;
                double randomZ = (random.nextDouble() - 0.5) * 0.1;
                player.setDeltaMovement(player.getDeltaMovement().add(randomX, 0, randomZ));
            }
        }
    }
}
