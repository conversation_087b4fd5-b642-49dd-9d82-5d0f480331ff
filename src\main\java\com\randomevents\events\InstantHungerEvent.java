package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

public class InstantHungerEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "instant_hunger";
    }
    
    @Override
    public String getName() {
        return "§c§lInstant Hunger!";
    }
    
    @Override
    public String getDescription() {
        return "All players lose all hunger instantly";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.getFoodData().setFoodLevel(0);
            player.getFoodData().setSaturation(0.0f);
        }
    }
}
