package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

public class SlowMotionEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "slow_motion";
    }
    
    @Override
    public String getName() {
        return "§7§lSlow Motion!";
    }
    
    @Override
    public String getDescription() {
        return "All players get slowness for 1 minute";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 60 * 20, 1)); // 1 minute, level 2
        }
    }
}
