package com.randomevents.commands;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.HashSet;
import java.util.Set;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

public class EliminationCeremony {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Set<ServerPlayer> frozenPlayers = new HashSet<>();
    private static boolean ceremonyActive = false;
    
    public static void execute(ServerPlayer targetPlayer) {
        MinecraftServer server = targetPlayer.getServer();
        if (server == null) return;

        ceremonyActive = true;
        ServerLevel level = targetPlayer.serverLevel();
        List<ServerPlayer> allPlayers = server.getPlayerList().getPlayers();
        List<ServerPlayer> spectators = new ArrayList<>();

        // Remove target player from spectators list
        for (ServerPlayer player : allPlayers) {
            if (!player.equals(targetPlayer)) {
                spectators.add(player);
            }
        }

        // Freeze ALL players (including target) - no movement allowed
        for (ServerPlayer player : allPlayers) {
            freezePlayer(player);
        }

        // Register movement prevention event handler
        MinecraftForge.EVENT_BUS.register(MovementPrevention.class);
        
        // Find execution location high in the sky
        BlockPos executionSite = findExecutionSite(level, targetPlayer.blockPosition());

        // Create the sky platform
        createSkyPlatform(level, executionSite);

        if (!spectators.isEmpty()) {
            // Phase 1: Teleport all spectators in a line
            teleportSpectatorsInLine(spectators, executionSite, level);
        }

        // Phase 2: Teleport target player to execution site (even if alone)
        teleportTargetToExecutionSite(targetPlayer, executionSite, level);

        // Phase 3: Wait 5 seconds, then start the elimination ceremony
        scheduler.schedule(() -> {
            startEliminationSequence(targetPlayer, spectators);
        }, 5L, TimeUnit.SECONDS);
    }
    
    private static BlockPos findExecutionSite(ServerLevel level, BlockPos startPos) {
        // Create execution site high in the sky (200 blocks up)
        int skyY = Math.max(200, level.getMaxBuildHeight() - 50);
        return new BlockPos(startPos.getX(), skyY, startPos.getZ());
    }

    private static void createSkyPlatform(ServerLevel level, BlockPos center) {
        // Create a 15x15 platform in the sky
        for (int x = -7; x <= 7; x++) {
            for (int z = -7; z <= 7; z++) {
                BlockPos platformPos = center.offset(x, -1, z); // One block below center
                level.setBlock(platformPos, net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }

        // Create a smaller 5x5 execution platform in the center
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                BlockPos executionPos = center.offset(x, 0, z);
                level.setBlock(executionPos, net.minecraft.world.level.block.Blocks.BLACKSTONE.defaultBlockState(), 3);
            }
        }

        // Add some dramatic pillars around the platform
        for (int i = 0; i < 4; i++) {
            int x = (i % 2 == 0) ? -6 : 6;
            int z = (i < 2) ? -6 : 6;

            for (int y = 0; y <= 5; y++) {
                BlockPos pillarPos = center.offset(x, y, z);
                level.setBlock(pillarPos, net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }
    }
    
    private static void teleportSpectatorsInLine(List<ServerPlayer> spectators, BlockPos executionSite, ServerLevel level) {
        // Teleport spectators in a line facing the execution site
        for (int i = 0; i < spectators.size(); i++) {
            ServerPlayer spectator = spectators.get(i);

            // Position spectators 8 blocks away from execution site in a line, ON TOP of the platform
            double x = executionSite.getX() - 8; // 8 blocks back
            double z = executionSite.getZ() + (i - spectators.size() / 2.0) * 2; // Spread them out
            double y = executionSite.getY() + 1; // One block ABOVE the platform

            // Calculate yaw to face the execution site
            float yaw = (float) Math.toDegrees(Math.atan2(executionSite.getZ() - z, executionSite.getX() - x));

            spectator.teleportTo(level, x + 0.5, y, z + 0.5, yaw, 0);
            
            // Apply effects to prevent actions (no slowness to avoid zoom)
            spectator.addEffect(new MobEffectInstance(MobEffects.JUMP, 1200, 250, false, false)); // Can't jump
            spectator.addEffect(new MobEffectInstance(MobEffects.WEAKNESS, 1200, 255, false, false)); // Can't break blocks
            spectator.addEffect(new MobEffectInstance(MobEffects.DIG_SLOWDOWN, 1200, 255, false, false)); // Can't mine
            
            // Play teleport sound
            level.playSound(null, spectator.blockPosition(), SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
        }
        
        // Show message to spectators
        for (ServerPlayer spectator : spectators) {
            spectator.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lYou have been gathered to witness the elimination...")));
        }
    }
    
    private static void teleportTargetToExecutionSite(ServerPlayer targetPlayer, BlockPos executionSite, ServerLevel level) {
        // Teleport target player to the execution site, ON TOP of the platform
        targetPlayer.teleportTo(level, executionSite.getX() + 0.5, executionSite.getY() + 1, executionSite.getZ() + 0.5, 0, 0);
        
        // Apply effects to prevent actions (no slowness to avoid zoom)
        targetPlayer.addEffect(new MobEffectInstance(MobEffects.JUMP, 1200, 250, false, false)); // Can't jump
        targetPlayer.addEffect(new MobEffectInstance(MobEffects.WEAKNESS, 1200, 255, false, false)); // Can't break blocks
        targetPlayer.addEffect(new MobEffectInstance(MobEffects.DIG_SLOWDOWN, 1200, 255, false, false)); // Can't mine
        
        // Play dramatic teleport sound
        level.playSound(null, executionSite, SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 2.0f, 0.5f);
        
        // Show message to target
        targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lYou have been brought for elimination...")));
    }
    
    private static void startEliminationSequence(ServerPlayer targetPlayer, List<ServerPlayer> spectators) {
        ServerLevel level = targetPlayer.serverLevel();
        String playerName = targetPlayer.getName().getString();

        // Phase 1: First ceremonial text - "We gather here today..." (4 seconds)
        for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
            // Clear any existing titles first
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                net.minecraft.network.chat.Component.literal("")));
            // Use subtitle for smaller text
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                net.minecraft.network.chat.Component.literal("§7We gather here today...")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                5, 75, 5)); // Fade in 0.25s, stay 3.75s, fade out 0.25s (total 4 seconds)
        }

        // Play solemn sound
        level.playSound(null, targetPlayer.blockPosition(), SoundEvents.WITHER_SPAWN, SoundSource.HOSTILE, 0.8f, 0.6f);

        // Phase 2: Second ceremonial text - "For the execution of playername..." (4 seconds after first text starts)
        scheduler.schedule(() -> {
            for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                // Clear previous subtitle first
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
                // Show second ceremonial text
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§cFor the execution of " + playerName + "...")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                    5, 75, 5)); // Fade in 0.25s, stay 3.75s, fade out 0.25s (total 4 seconds)
            }

            // Play dramatic sound
            level.playSound(null, targetPlayer.blockPosition(), SoundEvents.ANVIL_LAND, SoundSource.HOSTILE, 1.0f, 0.8f);

        }, 4L, TimeUnit.SECONDS); // 4 seconds after first text starts (right when first text ends)

        // Phase 3: Start levitation (after 8 seconds total - right when second text ends)
        scheduler.schedule(() -> {
            // Clear any existing titles and subtitles
            for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
            }

            // Start levitating the target player
            targetPlayer.addEffect(new MobEffectInstance(MobEffects.LEVITATION, 80, 1, false, false)); // 4 seconds of levitation

            // Play rising sound
            level.playSound(null, targetPlayer.blockPosition(), SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.PLAYERS, 1.5f, 1.2f);

            // Show levitation message to spectators
            for (ServerPlayer spectator : spectators) {
                spectator.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§l" + playerName + " is being lifted for execution...")));
            }

            // Show message to target
            targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYou are being lifted for execution...")));

        }, 8L, TimeUnit.SECONDS);

        // Phase 4: Explosion (after 11 seconds total - 3 seconds of levitation)
        scheduler.schedule(() -> {
            BlockPos explosionPos = targetPlayer.blockPosition();

            // Create massive visual explosion FIRST
            level.explode(null, explosionPos.getX(), explosionPos.getY(), explosionPos.getZ(),
                         5.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);

            // Kill the target player
            targetPlayer.hurt(level.damageSources().explosion(null, null), 1000.0f);

            // Play dramatic explosion sound
            level.playSound(null, explosionPos, SoundEvents.GENERIC_EXPLODE, SoundSource.HOSTILE, 3.0f, 0.5f);

            // Show elimination message AFTER the explosion (1 second delay)
            scheduler.schedule(() -> {
                for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                    // Clear title and use subtitle for smaller text
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                        net.minecraft.network.chat.Component.literal("")));
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                        net.minecraft.network.chat.Component.literal("§c" + playerName + " has been eliminated")));
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                        0, 60, 20)); // No fade in, stay 3s, fade out 1s
                }
            }, 1L, TimeUnit.SECONDS);

            // Release spectators after 5 seconds
            scheduler.schedule(() -> {
                for (ServerPlayer spectator : spectators) {
                    if (spectator.isAlive()) {
                        // Remove all movement restriction effects (no slowness was used)
                        spectator.removeEffect(MobEffects.JUMP);
                        spectator.removeEffect(MobEffects.WEAKNESS);
                        spectator.removeEffect(MobEffects.DIG_SLOWDOWN);

                        // Unfreeze the player
                        unfreezePlayer(spectator);

                        spectator.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§7§lThe execution is complete. You may move freely.")));
                    }
                }

                // Clear all titles and subtitles
                for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundClearTitlesPacket(true));
                }

                // End ceremony and unfreeze all players
                endCeremony();

            }, 5L, TimeUnit.SECONDS);

        }, 11L, TimeUnit.SECONDS);
    }

    private static void freezePlayer(ServerPlayer player) {
        frozenPlayers.add(player);
    }

    private static void unfreezePlayer(ServerPlayer player) {
        frozenPlayers.remove(player);
    }

    private static void endCeremony() {
        ceremonyActive = false;
        frozenPlayers.clear();

        // Unregister movement prevention event handler
        try {
            MinecraftForge.EVENT_BUS.unregister(MovementPrevention.class);
        } catch (Exception e) {
            // Ignore if already unregistered
        }
    }

    // Event handler class for movement prevention
    public static class MovementPrevention {
        @SubscribeEvent
        public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
            if (!ceremonyActive || event.phase != TickEvent.Phase.START) return;

            if (event.player instanceof ServerPlayer serverPlayer) {
                if (frozenPlayers.contains(serverPlayer)) {
                    // Cancel all movement by setting velocity to zero
                    Vec3 currentVelocity = serverPlayer.getDeltaMovement();

                    // Only cancel horizontal movement, allow gravity
                    if (Math.abs(currentVelocity.x) > 0.001 || Math.abs(currentVelocity.z) > 0.001) {
                        serverPlayer.setDeltaMovement(0, currentVelocity.y, 0);
                        serverPlayer.hurtMarked = true; // Force update
                    }

                    // Prevent jumping by canceling upward velocity
                    if (currentVelocity.y > 0.1) {
                        serverPlayer.setDeltaMovement(0, 0, 0);
                        serverPlayer.hurtMarked = true;
                    }
                }
            }
        }
    }
    

}
