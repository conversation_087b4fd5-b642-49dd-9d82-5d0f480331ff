package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.EntityMountEvent;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SuspiciousBoatEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean boatTrapActive = false;
    private static final Set<Entity> launchedPlayers = new HashSet<>();
    
    @Override
    public String getId() {
        return "suspicious_boat";
    }
    
    @Override
    public String getName() {
        return "§b§lSuspicious Boat";
    }
    
    @Override
    public String getDescription() {
        return "Players receive a mysterious boat with ominous consequences";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        boatTrapActive = true;
        launchedPlayers.clear();
        
        // Give each player a boat with a suspicious message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Give the player a boat
            ItemStack boat = new ItemStack(Items.OAK_BOAT, 1);
            
            // Try to add to inventory, or drop if full
            if (!player.getInventory().add(boat)) {
                player.drop(boat, false);
            }
            
            // Show the suspicious message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lTake this boat... §8You might need it later...")));
        }
        
        // Register event listener to detect boat mounting
        MinecraftForge.EVENT_BUS.register(new BoatTrapHandler());
        
        // Deactivate the trap after 5 minutes
        scheduler.schedule(() -> {
            boatTrapActive = false;
            launchedPlayers.clear();
            MinecraftForge.EVENT_BUS.unregister(BoatTrapHandler.class);
        }, 300L, TimeUnit.SECONDS); // 5 minutes
    }
    
    public static class BoatTrapHandler {
        @SubscribeEvent
        public static void onEntityMount(EntityMountEvent event) {
            if (!boatTrapActive || !(event.getEntityMounting() instanceof ServerPlayer player)) return;
            if (!(event.getEntityBeingMounted() instanceof Boat boat)) return;
            if (!event.isMounting()) return; // Only trigger when mounting, not dismounting

            // Check if this player has already been launched
            if (launchedPlayers.contains(player)) return;

            // Add player to launched set to prevent multiple launches
            launchedPlayers.add(player);

            // Wait a moment for the mounting to complete, then launch
            scheduler.schedule(() -> {
                if (player.getVehicle() == boat) {
                    launchPlayerInBoat(player, boat);
                }
            }, 500L, TimeUnit.MILLISECONDS); // 0.5 second delay
        }

        @SubscribeEvent
        public static void onPlayerInteract(PlayerInteractEvent.EntityInteract event) {
            if (!(event.getEntity() instanceof ServerPlayer player)) return;
            if (!(event.getTarget() instanceof Boat boat)) return;

            // Check if this player has already been launched
            if (launchedPlayers.contains(player)) return;

            // Add player to launched set to prevent multiple launches
            launchedPlayers.add(player);

            // Cancel the interaction to prevent normal boat behavior
            event.setCanceled(true);

            // Launch immediately when right-clicking
            launchPlayerInBoat(player, boat);
        }
        
        private static void launchPlayerInBoat(ServerPlayer player, Boat boat) {
            ServerLevel level = player.serverLevel();
            
            // Play dramatic sound
            level.playSound(null, player.blockPosition(), SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.PLAYERS, 1.0f, 0.5f);
            
            // Launch the boat (and player) EXTREMELY high into the air
            Vec3 launchVelocity = new Vec3(0, 5.0, 0); // Much higher upward velocity for more scare
            boat.setDeltaMovement(launchVelocity);
            
            // Show the betrayal message
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lNever trust anyone.")));
            
            // Play evil laugh sound
            scheduler.schedule(() -> {
                level.playSound(null, player.blockPosition(), SoundEvents.WITCH_CELEBRATE, SoundSource.PLAYERS, 1.0f, 0.8f);
            }, 1L, TimeUnit.SECONDS);
            
            // Monitor the player's fall and apply levitation when VERY close to ground (scary!)
            monitorPlayerFall(player, boat);
        }
        
        private static void monitorPlayerFall(ServerPlayer player, Boat boat) {
            // Check every 0.1 seconds for up to 45 seconds (more frequent checks for precision)
            for (int i = 0; i < 450; i++) { // 45 seconds / 0.1 = 450 checks
                scheduler.schedule(() -> {
                    if (!player.isAlive()) return;

                    ServerLevel level = player.serverLevel();
                    Vec3 playerPos = player.position();
                    Vec3 velocity = player.getDeltaMovement();

                    // Check if player is falling fast and VERY close to the ground (scary timing!)
                    if (velocity.y < -0.8) { // Player is falling with significant speed
                        // Check distance to ground
                        int groundY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING,
                                                    (int) playerPos.x, (int) playerPos.z);
                        double distanceToGround = playerPos.y - groundY;

                        // Only save them when VERY close to ground (3-5 blocks) for maximum scare!
                        if (distanceToGround <= 5 && distanceToGround > 1) {
                            // Apply levitation effect to stop the fall dramatically
                            player.addEffect(new MobEffectInstance(MobEffects.LEVITATION, 3 * 20, 2)); // 3 seconds of strong levitation

                            // Show dramatic last-second save message
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§c§lJUST KIDDING! §a§lLast second save!")));

                            // Play dramatic save sound
                            level.playSound(null, player.blockPosition(), SoundEvents.TOTEM_USE, SoundSource.PLAYERS, 1.0f, 1.2f);

                            // After levitation, apply slow falling for safe landing
                            scheduler.schedule(() -> {
                                if (player.isAlive()) {
                                    player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 10 * 20, 0)); // 10 seconds slow falling
                                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                        net.minecraft.network.chat.Component.literal("§7§lNow you can land safely... §8That was close!")));
                                }
                            }, 3L, TimeUnit.SECONDS);

                            // Remove player from monitoring (they've been saved)
                            launchedPlayers.remove(player);
                            return;
                        }
                    }

                    // If player has landed safely, remove from monitoring
                    if (player.onGround() && Math.abs(velocity.y) < 0.1) {
                        launchedPlayers.remove(player);

                        // Show final message
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§7§lHope you enjoyed the ride... §8Next time, be more suspicious.")));
                    }
                }, i * 100L, TimeUnit.MILLISECONDS); // Every 0.1 seconds for precise timing
            }

            // Failsafe: Remove player from monitoring after 45 seconds
            scheduler.schedule(() -> {
                launchedPlayers.remove(player);

                // Emergency levitation if somehow still falling
                if (player.isAlive() && player.getDeltaMovement().y < -0.5) {
                    player.addEffect(new MobEffectInstance(MobEffects.LEVITATION, 5 * 20, 1));
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§e§lEmergency levitation activated!")));

                    // Follow up with slow falling
                    scheduler.schedule(() -> {
                        if (player.isAlive()) {
                            player.addEffect(new MobEffectInstance(MobEffects.SLOW_FALLING, 15 * 20, 0));
                        }
                    }, 5L, TimeUnit.SECONDS);
                }
            }, 45L, TimeUnit.SECONDS);
        }
    }
}
