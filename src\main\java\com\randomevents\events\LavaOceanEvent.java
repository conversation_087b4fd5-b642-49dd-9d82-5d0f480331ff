package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LavaOceanEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<BlockPos> changedBlocks = new ArrayList<>();
    
    @Override
    public String getId() {
        return "lava_ocean";
    }
    
    @Override
    public String getName() {
        return "§c§lLava Ocean!";
    }
    
    @Override
    public String getDescription() {
        return "All water transforms into flowing lava in expanding waves - the oceans burn!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        changedBlocks.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lThe oceans are turning to lava! §7Get out of the water NOW!")));
        }
        
        // Start converting water to lava IMMEDIATELY and in waves
        scheduler.schedule(() -> {
            // Phase 1: Immediate conversion in close area
            int totalConverted = 0;
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Convert water in immediate area (50 block radius)
                for (int x = -50; x <= 50; x++) {
                    for (int y = -30; y <= 30; y++) {
                        for (int z = -50; z <= 50; z++) {
                            BlockPos checkPos = playerPos.offset(x, y, z);

                            if (level.isInWorldBounds(checkPos)) {
                                BlockState blockState = level.getBlockState(checkPos);

                                // Replace ALL water blocks (source and flowing)
                                if (blockState.is(Blocks.WATER) || blockState.getBlock() == Blocks.WATER) {
                                    // Store the original water block for restoration
                                    changedBlocks.add(checkPos);
                                    // FORCE replace with flowing lava - use flag 18 for immediate update without neighbor checks
                                    level.setBlock(checkPos, Blocks.LAVA.defaultBlockState(), 18);
                                    totalConverted++;
                                }
                                // Also replace any water-logged blocks
                                if (blockState.hasProperty(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED)) {
                                    if (blockState.getValue(net.minecraft.world.level.block.state.properties.BlockStateProperties.WATERLOGGED)) {
                                        changedBlocks.add(checkPos);
                                        level.setBlock(checkPos, Blocks.LAVA.defaultBlockState(), 18);
                                        totalConverted++;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Show first conversion message with count and dramatic sound
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lConverted " + totalConverted + " water blocks to lava! §7Phase 1/3 complete!")));

                // Play dramatic lava sound
                ServerLevel level = player.serverLevel();
                level.playSound(null, player.blockPosition(), SoundEvents.LAVA_POP, SoundSource.BLOCKS, 2.0f, 0.5f);
            }
        }, 1L, TimeUnit.SECONDS);

        // Phase 2: Expand conversion (after 3 seconds)
        scheduler.schedule(() -> {
            int totalConverted = 0;
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Convert water in larger area (150 block radius) - check every block
                for (int x = -150; x <= 150; x++) { // Check every block, not skipping
                    for (int y = -50; y <= 50; y++) {
                        for (int z = -150; z <= 150; z++) {
                            BlockPos checkPos = playerPos.offset(x, y, z);

                            if (level.isInWorldBounds(checkPos)) {
                                BlockState blockState = level.getBlockState(checkPos);

                                // Convert ALL water to lava - force immediate replacement
                                if (blockState.is(Blocks.WATER) || blockState.getBlock() == Blocks.WATER) {
                                    changedBlocks.add(checkPos);
                                    level.setBlock(checkPos, Blocks.LAVA.defaultBlockState(), 18);
                                    totalConverted++;
                                }
                            }
                        }
                    }
                }
            }

            // Show second conversion message with count
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lConverted " + totalConverted + " more blocks! §7Phase 2/3 complete!")));
            }
        }, 3L, TimeUnit.SECONDS);

        // Phase 3: MASSIVE conversion (after 6 seconds)
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // Convert water in MASSIVE area (300 block radius)
                for (int x = -300; x <= 300; x += 3) { // Skip blocks for speed
                    for (int y = -60; y <= 60; y += 3) {
                        for (int z = -300; z <= 300; z += 3) {
                            BlockPos checkPos = playerPos.offset(x, y, z);

                            if (level.isInWorldBounds(checkPos)) {
                                BlockState blockState = level.getBlockState(checkPos);

                                // Convert ALL water to lava - force immediate replacement
                                if (blockState.is(Blocks.WATER) || blockState.getBlock() == Blocks.WATER) {
                                    changedBlocks.add(checkPos);
                                    level.setBlock(checkPos, Blocks.LAVA.defaultBlockState(), 18);
                                }
                            }
                        }
                    }
                }
            }

            // Show final conversion message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lMASSIVE LAVA OCEAN! §7Phase 3/3 - The world burns!")));
            }
        }, 6L, TimeUnit.SECONDS);
        
        // Send periodic warnings during the lava ocean (every 3 seconds for 15 seconds)
        for (int i = 1; i <= 3; i++) { // 3 warnings over 15 seconds
            final int warningNumber = i;
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    String[] lavaMessages = {
                        "§c§lThe world is on fire!",
                        "§6§lLava everywhere - be careful!",
                        "§c§lThe oceans burn with molten rock!"
                    };

                    String message = lavaMessages[(warningNumber - 1) % lavaMessages.length];
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal(message)));
                }
            }, (6 + i * 3L), TimeUnit.SECONDS); // Every 3 seconds
        }

        // Convert lava back to water after 15 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§9§lThe lava is cooling... §7Water is returning!")));
            }
            
            // Convert all the lava back to water
            for (BlockPos pos : changedBlocks) {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    if (level.isInWorldBounds(pos) && level.getBlockState(pos).is(Blocks.LAVA)) {
                        level.setBlock(pos, Blocks.WATER.defaultBlockState(), 3);
                    }
                }
            }
            
            // Final message
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§9§lThe oceans have returned to normal! §7Safe to swim again.")));
                }
                changedBlocks.clear();
            }, 3L, TimeUnit.SECONDS);
            
        }, 21L, TimeUnit.SECONDS); // 15 seconds + 6 seconds initial delay
    }
}
