package com.randomevents.events;

import com.randomevents.RandomEventsMod;
import com.randomevents.manager.RandomEventManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class UltimateEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    // Events that work well together for maximum chaos
    private static final String[] ULTIMATE_COMPATIBLE_EVENTS = {
        "instant_hunger", "health_swap", "blindness_blast", "slow_motion", 
        "mining_fatigue", "levitation_lift", "instant_night", "lava_rain",
        "ice_age", "desert_storm", "sky_islands", "cow_chaos", "inventory_shuffle",
        "player_swap", "leaf_bomb", "backwards_controls", "baby_zombie_swarm",
        "hot_potato_bomb", "item_rain", "slippery_world", "super_speed",
        "chicken_rain", "dance_party", "bouncy_world", "musical_blocks",
        "everything_explodes", "time_dilation", "villager_takeover"
    };
    
    @Override
    public String getId() {
        return "ultimate_event";
    }
    
    @Override
    public String getName() {
        return "§4§l§kTHE ULTIMATE EVENT§r§4§l!";
    }
    
    @Override
    public String getDescription() {
        return "Combines 5 random events simultaneously for MAXIMUM CHAOS!";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show dramatic warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§l§kTHE ULTIMATE EVENT§r §c- PREPARE FOR ABSOLUTE CHAOS!")));
        }
        
        // Wait 5 seconds for dramatic effect, then trigger 5 random events
        scheduler.schedule(() -> {
            RandomEventManager manager = RandomEventsMod.getEventManager();
            if (manager != null) {
                // Get 5 random compatible events
                List<String> eventPool = new ArrayList<>();
                Collections.addAll(eventPool, ULTIMATE_COMPATIBLE_EVENTS);
                Collections.shuffle(eventPool);
                
                // Take the first 5 events
                List<String> selectedEvents = eventPool.subList(0, Math.min(5, eventPool.size()));
                
                // Show which events are being triggered
                StringBuilder eventNames = new StringBuilder("§4§lULTIMATE CHAOS: ");
                for (int i = 0; i < selectedEvents.size(); i++) {
                    if (i > 0) eventNames.append("§7 + ");
                    eventNames.append("§e").append(selectedEvents.get(i));
                }
                
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal(eventNames.toString())));
                }
                
                // Trigger each event with a small delay between them
                for (int i = 0; i < selectedEvents.size(); i++) {
                    final String eventId = selectedEvents.get(i);
                    final int eventNumber = i + 1;
                    scheduler.schedule(() -> {
                        // Show countdown
                        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§4§lActivating Event " + eventNumber + "/5: §e" + eventId)));
                        }
                        manager.triggerSpecificEvent(eventId);
                    }, i * 3L, TimeUnit.SECONDS); // 3 second delay between each event
                }
                
                // Final message
                scheduler.schedule(() -> {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§4§l§kULTIMATE CHAOS UNLEASHED!§r §cSurvive if you can!")));
                    }
                }, 15L, TimeUnit.SECONDS);
            }
        }, 5L, TimeUnit.SECONDS);
    }
}
