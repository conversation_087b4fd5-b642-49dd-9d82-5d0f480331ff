package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PlayerSwapEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "player_swap";
    }
    
    @Override
    public String getName() {
        return "§3§lPlayer Swap!";
    }
    
    @Override
    public String getDescription() {
        return "All players swap positions with each other";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> players = new ArrayList<>(server.getPlayerList().getPlayers());
        if (players.size() < 2) return;
        
        // Store all player positions and levels
        List<BlockPos> positions = new ArrayList<>();
        List<ServerLevel> levels = new ArrayList<>();
        List<Float> yRots = new ArrayList<>();
        List<Float> xRots = new ArrayList<>();
        
        for (ServerPlayer player : players) {
            positions.add(player.blockPosition());
            levels.add(player.serverLevel());
            yRots.add(player.getYRot());
            xRots.add(player.getXRot());
        }
        
        // Shuffle the positions
        Collections.shuffle(positions);
        Collections.shuffle(levels);
        
        // Teleport players to shuffled positions
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);
            BlockPos newPos = positions.get(i);
            ServerLevel newLevel = levels.get(i);
            
            player.teleportTo(newLevel, newPos.getX() + 0.5, newPos.getY(), newPos.getZ() + 0.5, 
                            yRots.get(i), xRots.get(i));
        }
    }
}
