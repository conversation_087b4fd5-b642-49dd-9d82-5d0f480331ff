package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

public class MiningFatigueEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "mining_fatigue";
    }
    
    @Override
    public String getName() {
        return "§8§lMining Fatigue!";
    }
    
    @Override
    public String getDescription() {
        return "All players get mining fatigue for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.addEffect(new MobEffectInstance(MobEffects.DIG_SLOWDOWN, 120 * 20, 1)); // 2 minutes, level 2
        }
    }
}
