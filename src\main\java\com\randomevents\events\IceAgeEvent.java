package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.Random;

public class IceAgeEvent extends RandomEvent {
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "ice_age";
    }
    
    @Override
    public String getName() {
        return "§b§lIce Age!";
    }
    
    @Override
    public String getDescription() {
        return "All water in 50 block radius turns to ice";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Freeze the MASSIVE ground under and around players (300x300 area!)
            for (int x = -150; x <= 150; x++) {
                for (int z = -150; z <= 150; z++) {
                    // Check the floor level (blocks under the player)
                    for (int y = -5; y <= 2; y++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            BlockState blockState = level.getBlockState(checkPos);

                            // Turn water into ice
                            if (blockState.is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.ICE.defaultBlockState(), 3);
                            }
                            // Turn ALL solid ground blocks into ice
                            else if (y <= 1 && (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                                               blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.COBBLESTONE) ||
                                               blockState.is(Blocks.SAND) || blockState.is(Blocks.GRAVEL) ||
                                               blockState.is(Blocks.ANDESITE) || blockState.is(Blocks.GRANITE) ||
                                               blockState.is(Blocks.DIORITE) || blockState.is(Blocks.DEEPSLATE))) {
                                // 80% chance to turn ground blocks to ice
                                if (random.nextInt(5) < 4) {
                                    level.setBlock(checkPos, Blocks.ICE.defaultBlockState(), 3);
                                }
                            }
                        }
                    }
                }
            }

            // Also freeze any water in an even larger radius
            for (int x = -200; x <= 200; x++) {
                for (int y = -30; y <= 30; y++) {
                    for (int z = -200; z <= 200; z++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        // Check if it's within the world bounds
                        if (level.isInWorldBounds(checkPos)) {
                            if (level.getBlockState(checkPos).is(Blocks.WATER)) {
                                level.setBlock(checkPos, Blocks.ICE.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            }
        }
    }
}
