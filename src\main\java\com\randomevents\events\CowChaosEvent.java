package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.animal.Cow;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CowChaosEvent extends RandomEvent {
    private static final Random random = new Random();
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Override
    public String getId() {
        return "cow_chaos";
    }

    @Override
    public String getName() {
        return "§6§lCow Chaos!";
    }

    @Override
    public String getDescription() {
        return "20 aggressive cows spawn and chase players around for 30 seconds";
    }

    @Override
    public void execute(MinecraftServer server) {
        List<Cow> spawnedCows = new ArrayList<>();

        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Spawn 20 cows around the player
            for (int i = 0; i < 20; i++) {
                int x = playerPos.getX() + random.nextInt(20) - 10;
                int z = playerPos.getZ() + random.nextInt(20) - 10;
                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);

                BlockPos spawnPos = new BlockPos(x, y, z);

                // Make sure the spawn position is safe
                if (level.getBlockState(spawnPos).isAir() &&
                    level.getBlockState(spawnPos.above()).isAir() &&
                    !level.getBlockState(spawnPos.below()).isAir()) {

                    Cow cow = EntityType.COW.create(level);
                    if (cow != null) {
                        cow.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);

                        // Make the cow faster and more aggressive
                        cow.getAttribute(net.minecraft.world.entity.ai.attributes.Attributes.MOVEMENT_SPEED)
                            .setBaseValue(0.4); // Faster than normal

                        level.addFreshEntity(cow);
                        spawnedCows.add(cow);
                    }
                }
            }
        }

        // Make cows chase and push players for 30 seconds
        for (int i = 0; i < 30; i++) {
            scheduler.schedule(() -> {
                for (Cow cow : spawnedCows) {
                    if (cow.isAlive()) {
                        ServerLevel level = (ServerLevel) cow.level();

                        // Find nearest player
                        ServerPlayer nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;

                        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
                            if (player.serverLevel() == level) {
                                double distance = cow.distanceToSqr(player);
                                if (distance < nearestDistance) {
                                    nearestDistance = distance;
                                    nearestPlayer = player;
                                }
                            }
                        }

                        // Chase the nearest player
                        if (nearestPlayer != null && nearestDistance < 400) { // Within 20 blocks
                            Vec3 direction = nearestPlayer.position().subtract(cow.position()).normalize();

                            // Move cow towards player
                            cow.setDeltaMovement(direction.scale(0.3));

                            // If close enough, push the player
                            if (nearestDistance < 4) { // Within 2 blocks
                                Vec3 pushDirection = nearestPlayer.position().subtract(cow.position()).normalize();
                                nearestPlayer.setDeltaMovement(nearestPlayer.getDeltaMovement().add(
                                    pushDirection.x * 0.5, 0.2, pushDirection.z * 0.5));
                            }
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }

        // Remove all cows after 30 seconds
        scheduler.schedule(() -> {
            for (Cow cow : spawnedCows) {
                if (cow.isAlive()) {
                    cow.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                }
            }
        }, 30L, TimeUnit.SECONDS);
    }
}
