package com.randomevents.network;

import com.randomevents.client.EventCounterRenderer;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

public class EventCounterPacket {
    private final String counterText;
    private final boolean show;
    
    public EventCounterPacket(String counterText, boolean show) {
        this.counterText = counterText;
        this.show = show;
    }
    
    public EventCounterPacket(FriendlyByteBuf buf) {
        this.counterText = buf.readUtf();
        this.show = buf.readBoolean();
    }
    
    public void toBytes(FriendlyByteBuf buf) {
        buf.writeUtf(counterText);
        buf.writeBoolean(show);
    }
    
    public boolean handle(Supplier<NetworkEvent.Context> supplier) {
        NetworkEvent.Context context = supplier.get();
        context.enqueueWork(() -> {
            // This code runs on the client side
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                if (show) {
                    EventCounterRenderer.setCounterText(counterText);
                } else {
                    EventCounterRenderer.hideCounter();
                }
            });
        });
        return true;
    }
}
