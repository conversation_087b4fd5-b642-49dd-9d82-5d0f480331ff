package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SuperSpeedEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static boolean superSpeedActive = false;
    
    @Override
    public String getId() {
        return "super_speed";
    }
    
    @Override
    public String getName() {
        return "§a§lSuper Speed!";
    }
    
    @Override
    public String getDescription() {
        return "All players move 5x faster but can't control direction well for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        superSpeedActive = true;
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§a§lYou feel incredibly fast... §7But something's wrong!")));
            
            // Give extreme speed effect
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 120 * 20, 4)); // 2 minutes, level 5
        }
        
        // Register event listener for chaotic movement
        MinecraftForge.EVENT_BUS.register(new SuperSpeedEventHandler());
        
        // Stop super speed effect after 2 minutes
        scheduler.schedule(() -> {
            superSpeedActive = false;
            MinecraftForge.EVENT_BUS.unregister(SuperSpeedEventHandler.class);
        }, 120L, TimeUnit.SECONDS);
    }
    
    public static class SuperSpeedEventHandler {
        @SubscribeEvent
        public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!superSpeedActive || !(event.getEntity() instanceof ServerPlayer player)) return;
            
            Vec3 motion = player.getDeltaMovement();
            
            // If player is moving horizontally, add chaotic elements
            if (Math.abs(motion.x) > 0.1 || Math.abs(motion.z) > 0.1) {
                // Add random direction changes (hard to control)
                double chaos = 0.3; // How much chaos to add
                double randomX = (Math.random() - 0.5) * chaos;
                double randomZ = (Math.random() - 0.5) * chaos;
                
                // Apply chaotic movement
                player.setDeltaMovement(
                    motion.x + randomX,
                    motion.y,
                    motion.z + randomZ
                );
                
                // Occasionally add a sudden direction change
                if (Math.random() < 0.05) { // 5% chance per tick
                    double suddenChange = 0.8;
                    player.setDeltaMovement(
                        motion.x + (Math.random() - 0.5) * suddenChange,
                        motion.y,
                        motion.z + (Math.random() - 0.5) * suddenChange
                    );
                }
            }
        }
    }
}
