package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.level.block.Blocks;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TrustFallEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, BlockPos> playerPlatforms = new HashMap<>();
    private static final Set<ServerPlayer> playersWhoJumped = new HashSet<>();
    private static final Set<ServerPlayer> playersWhoSurvived = new HashSet<>();
    private static boolean eventActive = false;
    private static BlockPos centralLandingArea;

    @Override
    public String getId() {
        return "trust_fall";
    }

    @Override
    public String getName() {
        return "§e§lTrust Fall!";
    }

    @Override
    public String getDescription() {
        return "Players must jump from high platforms and trust others to catch them with water";
    }

    @Override
    public void execute(MinecraftServer server) {
        eventActive = true;
        playerPlatforms.clear();
        playersWhoJumped.clear();
        playersWhoSurvived.clear();
        
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        if (players.size() < 2) {
            // Not enough players for trust fall
            for (ServerPlayer player : players) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lNot enough players for Trust Fall event.")));
            }
            return;
        }

        // Show warning message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lPreparing for the ultimate trust exercise...")));
        }

        // Wait 3 seconds then teleport players
        scheduler.schedule(() -> {
            setupTrustFallArena(server);
        }, 3L, TimeUnit.SECONDS);
    }

    private void setupTrustFallArena(MinecraftServer server) {
        ServerLevel level = server.overworld();
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        
        // Find a central location (average of all player positions)
        int avgX = 0, avgZ = 0;
        for (ServerPlayer player : players) {
            avgX += player.blockPosition().getX();
            avgZ += player.blockPosition().getZ();
        }
        avgX /= players.size();
        avgZ /= players.size();
        
        int groundY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, avgX, avgZ);
        centralLandingArea = new BlockPos(avgX, groundY, avgZ);
        
        // Create landing area (15x15 platform)
        createLandingArea(level, centralLandingArea);
        
        // Create platforms for each player
        int platformHeight = groundY + 25; // 25 blocks high
        int radius = 12; // Distance from center
        
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);
            
            // Calculate position around circle
            double angle = (2 * Math.PI * i) / players.size();
            int x = avgX + (int)(Math.cos(angle) * radius);
            int z = avgZ + (int)(Math.sin(angle) * radius);
            
            BlockPos platformPos = new BlockPos(x, platformHeight, z);
            
            // Create platform
            createPlatform(level, platformPos);
            playerPlatforms.put(player, platformPos);
            
            // Teleport player to platform
            player.teleportTo(level, x + 0.5, platformHeight + 1, z + 0.5, 0, 0);
            
            // Apply effects to prevent movement initially
            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 200, 255, false, false));
            player.addEffect(new MobEffectInstance(MobEffects.JUMP, 200, 250, false, false));
        }

        // Show instructions
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lTRUST FALL CHALLENGE")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                net.minecraft.network.chat.Component.literal("§7Jump and trust others to catch you with water!")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                10, 100, 10)); // Fade in 0.5s, stay 5s, fade out 0.5s
        }

        // Play dramatic sound
        level.playSound(null, centralLandingArea, SoundEvents.ANVIL_LAND, SoundSource.BLOCKS, 2.0f, 0.5f);

        // Start the challenge after 5 seconds
        scheduler.schedule(() -> {
            startTrustFallChallenge(server);
        }, 5L, TimeUnit.SECONDS);
    }

    private void createLandingArea(ServerLevel level, BlockPos center) {
        // Create 15x15 stone platform
        for (int x = -7; x <= 7; x++) {
            for (int z = -7; z <= 7; z++) {
                BlockPos pos = center.offset(x, 0, z);
                level.setBlock(pos, Blocks.STONE.defaultBlockState(), 3);
                
                // Clear area above
                for (int y = 1; y <= 30; y++) {
                    level.setBlock(pos.above(y), Blocks.AIR.defaultBlockState(), 3);
                }
            }
        }
        
        // Add some decoration around the edges
        for (int x = -8; x <= 8; x++) {
            for (int z = -8; z <= 8; z++) {
                if (Math.abs(x) == 8 || Math.abs(z) == 8) {
                    BlockPos pos = center.offset(x, 1, z);
                    level.setBlock(pos, Blocks.IRON_BARS.defaultBlockState(), 3);
                }
            }
        }
    }

    private void createPlatform(ServerLevel level, BlockPos center) {
        // Create 3x3 platform
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos pos = center.offset(x, 0, z);
                level.setBlock(pos, Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }
        
        // Add safety railings (except front)
        level.setBlock(center.offset(-1, 1, -1), Blocks.IRON_BARS.defaultBlockState(), 3);
        level.setBlock(center.offset(0, 1, -1), Blocks.IRON_BARS.defaultBlockState(), 3);
        level.setBlock(center.offset(1, 1, -1), Blocks.IRON_BARS.defaultBlockState(), 3);
        level.setBlock(center.offset(-1, 1, 0), Blocks.IRON_BARS.defaultBlockState(), 3);
        level.setBlock(center.offset(1, 1, 0), Blocks.IRON_BARS.defaultBlockState(), 3);
    }

    private void startTrustFallChallenge(MinecraftServer server) {
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        
        // Remove movement restrictions
        for (ServerPlayer player : players) {
            player.removeEffect(MobEffects.MOVEMENT_SLOWDOWN);
            player.removeEffect(MobEffects.JUMP);
        }

        // Show challenge start message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lJUMP NOW!")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                net.minecraft.network.chat.Component.literal("§e60 seconds to all jump and survive!")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                0, 60, 10)); // No fade in, stay 3s, fade out 0.5s
        }

        // Give players water buckets
        for (ServerPlayer player : players) {
            player.getInventory().add(new net.minecraft.world.item.ItemStack(net.minecraft.world.item.Items.WATER_BUCKET, 3));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lYou received 3 water buckets! Use them to catch falling players!")));
        }

        // Start monitoring for jumps and falls
        monitorTrustFall(server);

        // Countdown and failure check after 60 seconds
        scheduler.schedule(() -> {
            checkTrustFallResults(server);
        }, 60L, TimeUnit.SECONDS);
    }

    private void monitorTrustFall(MinecraftServer server) {
        // Check every second for players who have jumped
        for (int i = 0; i < 60; i++) {
            scheduler.schedule(() -> {
                if (!eventActive) return;
                
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (!playersWhoJumped.contains(player) && playerPlatforms.containsKey(player)) {
                        BlockPos platform = playerPlatforms.get(player);
                        BlockPos playerPos = player.blockPosition();
                        
                        // Check if player has left their platform (jumped)
                        if (playerPos.getY() < platform.getY() - 2) {
                            playersWhoJumped.add(player);
                            
                            // Announce the jump
                            for (ServerPlayer allPlayer : server.getPlayerList().getPlayers()) {
                                allPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§e§l" + player.getName().getString() + " has jumped! Catch them!")));
                            }
                        }
                    }
                    
                    // Check if player has landed safely (in water or on soft blocks)
                    if (playersWhoJumped.contains(player) && !playersWhoSurvived.contains(player)) {
                        if (player.blockPosition().getY() <= centralLandingArea.getY() + 2) {
                            // Player has landed - check if they survived
                            if (player.isAlive()) {
                                playersWhoSurvived.add(player);
                                
                                for (ServerPlayer allPlayer : server.getPlayerList().getPlayers()) {
                                    allPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                        net.minecraft.network.chat.Component.literal("§a§l" + player.getName().getString() + " landed safely!")));
                                }
                            }
                        }
                    }
                }
            }, i * 1000L, TimeUnit.MILLISECONDS);
        }
    }

    private void checkTrustFallResults(MinecraftServer server) {
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        
        // Check if everyone jumped and survived
        boolean allJumped = true;
        boolean allSurvived = true;
        
        for (ServerPlayer player : players) {
            if (!playersWhoJumped.contains(player)) {
                allJumped = false;
            }
            if (!playersWhoSurvived.contains(player)) {
                allSurvived = false;
            }
        }

        if (allJumped && allSurvived) {
            // SUCCESS!
            for (ServerPlayer player : players) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§a§lTRUST FALL SUCCESS!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§eEveryone trusted and survived!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                    0, 80, 20)); // Stay 4s, fade out 1s
            }
        } else {
            // FAILURE - EVERYONE EXPLODES!
            for (ServerPlayer player : players) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lTRUST FALL FAILED!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§4Trust was broken - everyone pays!")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                    0, 60, 0)); // Stay 3s
                
                // Explode all players
                BlockPos playerPos = player.blockPosition();
                server.overworld().explode(null, playerPos.getX(), playerPos.getY(), playerPos.getZ(), 
                    4.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
                
                player.hurt(server.overworld().damageSources().explosion(null, null), 1000.0f);
            }
        }

        // Cleanup after 5 seconds
        scheduler.schedule(() -> {
            cleanupTrustFall(server);
        }, 5L, TimeUnit.SECONDS);
    }

    private void cleanupTrustFall(MinecraftServer server) {
        eventActive = false;
        ServerLevel level = server.overworld();
        
        // Remove platforms
        for (BlockPos platform : playerPlatforms.values()) {
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    for (int y = 0; y <= 2; y++) {
                        level.setBlock(platform.offset(x, y, z), Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
        }
        
        // Remove landing area
        for (int x = -8; x <= 8; x++) {
            for (int z = -8; z <= 8; z++) {
                for (int y = 0; y <= 2; y++) {
                    BlockPos pos = centralLandingArea.offset(x, y, z);
                    if (!level.getBlockState(pos).isAir()) {
                        level.setBlock(pos, Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
        }
        
        // Clear tracking data
        playerPlatforms.clear();
        playersWhoJumped.clear();
        playersWhoSurvived.clear();
        
        // Show cleanup message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            if (player.isAlive()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe trust fall arena disappears...")));
            }
        }
    }
}
