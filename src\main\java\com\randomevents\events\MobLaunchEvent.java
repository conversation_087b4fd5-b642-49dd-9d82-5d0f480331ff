package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.*;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.monster.*;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MobLaunchEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<Entity, Long> launchedMobs = new HashMap<>();
    
    @Override
    public String getId() {
        return "mob_launch";
    }
    
    @Override
    public String getName() {
        return "§e§lMob Launch!";
    }
    
    @Override
    public String getDescription() {
        return "All nearby mobs fly into the air and drop valuable loot when they land";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        launchedMobs.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lAnti-gravity field detected... §6All creatures are about to take flight!")));
        }
        
        // Wait 3 seconds then spawn additional mobs and launch everything
        scheduler.schedule(() -> {
            List<Entity> allLaunchedMobs = new ArrayList<>();

            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();
                BlockPos playerPos = player.blockPosition();

                // First, spawn 5-8 additional mobs near the player
                int additionalMobs = 5 + random.nextInt(4); // 5-8 mobs
                spawnAdditionalMobs(level, playerPos, additionalMobs);

                // Find all mobs within 50 blocks of each player (including newly spawned ones)
                AABB searchArea = new AABB(
                    playerPos.getX() - 50, playerPos.getY() - 20, playerPos.getZ() - 50,
                    playerPos.getX() + 50, playerPos.getY() + 20, playerPos.getZ() + 50
                );

                List<Entity> nearbyMobs = level.getEntities((Entity) null, searchArea, entity ->
                    entity instanceof LivingEntity &&
                    !(entity instanceof ServerPlayer) && // Don't launch players
                    entity.isAlive()
                );

                // Launch all found mobs (existing + newly spawned)
                for (Entity mob : nearbyMobs) {
                    if (!launchedMobs.containsKey(mob)) { // Prevent double-launching
                        launchMob(mob, level);
                        launchedMobs.put(mob, System.currentTimeMillis());
                        allLaunchedMobs.add(mob);
                    }
                }

                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§lMOB LAUNCH ACTIVATED! §6" + nearbyMobs.size() + " creatures launched! (+" + additionalMobs + " spawned)")));
            }
            
            // Monitor launched mobs for landing and loot dropping
            monitorLandingMobs(server, allLaunchedMobs);
            
        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }

    private List<Entity> spawnAdditionalMobs(ServerLevel level, BlockPos playerPos, int count) {
        List<Entity> spawnedMobs = new ArrayList<>();

        // Define mob types to spawn (mix of passive and hostile)
        EntityType<?>[] mobTypes = {
            EntityType.COW, EntityType.PIG, EntityType.SHEEP, EntityType.CHICKEN,
            EntityType.ZOMBIE, EntityType.SKELETON, EntityType.CREEPER,
            EntityType.VILLAGER, EntityType.ENDERMAN
        };

        for (int i = 0; i < count; i++) {
            // Choose random mob type
            EntityType<?> mobType = mobTypes[random.nextInt(mobTypes.length)];

            // Find a safe spawn position around the player (15-25 blocks away)
            for (int attempts = 0; attempts < 10; attempts++) {
                int x = playerPos.getX() + (15 + random.nextInt(11)) * (random.nextBoolean() ? 1 : -1); // 15-25 blocks away
                int z = playerPos.getZ() + (15 + random.nextInt(11)) * (random.nextBoolean() ? 1 : -1);
                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);

                BlockPos spawnPos = new BlockPos(x, y, z);

                // Make sure spawn position is safe
                if (level.getBlockState(spawnPos).isAir() &&
                    level.getBlockState(spawnPos.above()).isAir() &&
                    level.getBlockState(spawnPos.above().above()).isAir() &&
                    !level.getBlockState(spawnPos.below()).isAir()) {

                    // Create the mob
                    Entity mob = mobType.create(level);
                    if (mob != null) {
                        mob.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);
                        level.addFreshEntity(mob);
                        spawnedMobs.add(mob);

                        // Play spawn sound
                        level.playSound(null, spawnPos, SoundEvents.CHICKEN_EGG, SoundSource.NEUTRAL, 0.5f, 1.0f + random.nextFloat() * 0.5f);
                        break; // Successfully spawned, move to next mob
                    }
                }
            }
        }

        return spawnedMobs;
    }

    private void launchMob(Entity mob, ServerLevel level) {
        // Calculate launch velocity (random direction, high upward force)
        double launchX = (random.nextDouble() - 0.5) * 1.0; // Random horizontal direction
        double launchY = 1.5 + random.nextDouble() * 1.0; // Strong upward force (1.5-2.5)
        double launchZ = (random.nextDouble() - 0.5) * 1.0; // Random horizontal direction
        
        // Apply launch velocity
        mob.setDeltaMovement(launchX, launchY, launchZ);
        
        // Play launch sound
        level.playSound(null, mob.blockPosition(), SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.NEUTRAL, 0.8f, 1.0f + random.nextFloat() * 0.5f);
        
        // Add some particle effects if possible (visual feedback)
        if (random.nextInt(3) == 0) { // 33% chance for extra sound
            level.playSound(null, mob.blockPosition(), SoundEvents.GENERIC_EXPLODE, SoundSource.NEUTRAL, 0.3f, 2.0f);
        }
    }
    
    private void monitorLandingMobs(MinecraftServer server, List<Entity> launchedMobs) {
        // Check every 0.5 seconds for 30 seconds to see if mobs have landed
        for (int i = 0; i < 60; i++) { // 30 seconds / 0.5 = 60 checks
            scheduler.schedule(() -> {
                List<Entity> mobsToRemove = new ArrayList<>();
                
                for (Entity mob : launchedMobs) {
                    if (!mob.isAlive()) {
                        mobsToRemove.add(mob);
                        continue;
                    }
                    
                    // Check if mob has landed (low Y velocity and on ground)
                    Vec3 velocity = mob.getDeltaMovement();
                    if (Math.abs(velocity.y) < 0.1 && mob.onGround()) {
                        // Mob has landed! Drop loot
                        dropLootForMob(mob, (ServerLevel) mob.level());
                        mobsToRemove.add(mob);
                    }
                }
                
                // Remove landed mobs from monitoring
                launchedMobs.removeAll(mobsToRemove);
                for (Entity mob : mobsToRemove) {
                    launchedMobs.remove(mob);
                }
                
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 0.5 seconds
        }
        
        // Final cleanup after 30 seconds
        scheduler.schedule(() -> {
            // Drop loot for any remaining mobs that might still be in the air
            for (Entity mob : launchedMobs) {
                if (mob.isAlive()) {
                    dropLootForMob(mob, (ServerLevel) mob.level());
                }
            }
            launchedMobs.clear();
            
            // Show completion message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe anti-gravity field fades... §eCollect your rewards!")));
            }
        }, 30L, TimeUnit.SECONDS);
    }
    
    private void dropLootForMob(Entity mob, ServerLevel level) {
        BlockPos mobPos = mob.blockPosition();
        
        // Determine loot based on mob type
        List<ItemStack> lootItems = new ArrayList<>();
        
        if (mob instanceof Cow || mob instanceof Sheep || mob instanceof Pig) {
            // Farm animals drop food and some valuables
            lootItems.add(new ItemStack(Items.COOKED_BEEF, 2 + random.nextInt(4))); // 2-5 cooked beef
            lootItems.add(new ItemStack(Items.LEATHER, 1 + random.nextInt(3))); // 1-3 leather
            if (random.nextInt(3) == 0) { // 33% chance
                lootItems.add(new ItemStack(Items.IRON_INGOT, 1 + random.nextInt(3))); // 1-3 iron
            }
        } else if (mob instanceof Chicken) {
            lootItems.add(new ItemStack(Items.COOKED_CHICKEN, 1 + random.nextInt(3))); // 1-3 cooked chicken
            lootItems.add(new ItemStack(Items.FEATHER, 2 + random.nextInt(4))); // 2-5 feathers
            if (random.nextInt(4) == 0) { // 25% chance
                lootItems.add(new ItemStack(Items.GOLD_INGOT, 1 + random.nextInt(2))); // 1-2 gold
            }
        } else if (mob instanceof Villager) {
            // Villagers drop valuable trade goods
            lootItems.add(new ItemStack(Items.EMERALD, 2 + random.nextInt(4))); // 2-5 emeralds
            lootItems.add(new ItemStack(Items.BREAD, 3 + random.nextInt(5))); // 3-7 bread
            if (random.nextInt(2) == 0) { // 50% chance
                lootItems.add(new ItemStack(Items.DIAMOND, 1)); // 1 diamond
            }
        } else if (mob instanceof Zombie || mob instanceof Skeleton || mob instanceof Creeper) {
            // Hostile mobs drop combat loot
            lootItems.add(new ItemStack(Items.IRON_INGOT, 2 + random.nextInt(4))); // 2-5 iron
            lootItems.add(new ItemStack(Items.GOLD_INGOT, 1 + random.nextInt(3))); // 1-3 gold
            if (random.nextInt(3) == 0) { // 33% chance
                lootItems.add(new ItemStack(Items.DIAMOND, 1 + random.nextInt(2))); // 1-2 diamonds
            }
            if (mob instanceof Skeleton) {
                lootItems.add(new ItemStack(Items.ARROW, 5 + random.nextInt(10))); // 5-14 arrows
            }
        } else if (mob instanceof EnderMan) {
            // Endermen drop rare loot
            lootItems.add(new ItemStack(Items.ENDER_PEARL, 2 + random.nextInt(3))); // 2-4 ender pearls
            lootItems.add(new ItemStack(Items.DIAMOND, 1 + random.nextInt(3))); // 1-3 diamonds
            if (random.nextInt(4) == 0) { // 25% chance
                lootItems.add(new ItemStack(Items.NETHERITE_SCRAP, 1)); // 1 netherite scrap
            }
        } else {
            // Default loot for other mobs
            lootItems.add(new ItemStack(Items.IRON_INGOT, 1 + random.nextInt(3))); // 1-3 iron
            if (random.nextInt(2) == 0) { // 50% chance
                lootItems.add(new ItemStack(Items.GOLD_INGOT, 1 + random.nextInt(2))); // 1-2 gold
            }
        }
        
        // Always add some food
        ItemStack[] foods = {
            new ItemStack(Items.COOKED_PORKCHOP, 1 + random.nextInt(3)),
            new ItemStack(Items.BREAD, 2 + random.nextInt(4)),
            new ItemStack(Items.APPLE, 1 + random.nextInt(4)),
            new ItemStack(Items.CARROT, 2 + random.nextInt(5))
        };
        lootItems.add(foods[random.nextInt(foods.length)]);
        
        // Drop all loot items
        for (ItemStack loot : lootItems) {
            ItemEntity lootEntity = new ItemEntity(level, 
                mobPos.getX() + 0.5 + (random.nextDouble() - 0.5) * 2, 
                mobPos.getY() + 1, 
                mobPos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 2, 
                loot);
            
            // Give loot some upward velocity and random spread
            lootEntity.setDeltaMovement(
                (random.nextDouble() - 0.5) * 0.4, // Random X
                0.2 + random.nextDouble() * 0.3, // Upward Y
                (random.nextDouble() - 0.5) * 0.4  // Random Z
            );
            
            // Make loot glow briefly
            lootEntity.setGlowingTag(true);
            
            level.addFreshEntity(lootEntity);
        }
        
        // Play loot drop sound
        level.playSound(null, mobPos, SoundEvents.PLAYER_LEVELUP, SoundSource.NEUTRAL, 0.5f, 1.5f + random.nextFloat() * 0.5f);
        
        // Show loot message to nearby players
        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
            if (player.serverLevel() == level && player.distanceToSqr(Vec3.atCenterOf(mobPos)) < 1600) { // Within 40 blocks
                String mobName = mob.getDisplayName().getString();
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§e§l" + mobName + " landed! §6Loot scattered!")));
            }
        }
    }
}
