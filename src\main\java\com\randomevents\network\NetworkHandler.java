package com.randomevents.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.simple.SimpleChannel;

public class NetworkHandler {
    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        new ResourceLocation("randomevents", "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    private static int packetId = 0;
    
    public static void register() {
        INSTANCE.messageBuilder(EventCounterPacket.class, packetId++, NetworkDirection.PLAY_TO_CLIENT)
            .decoder(EventCounterPacket::new)
            .encoder(EventCounterPacket::toBytes)
            .consumerMainThread(EventCounterPacket::handle)
            .add();
    }
    
    public static void sendToPlayer(EventCounterPacket packet, ServerPlayer player) {
        INSTANCE.send(PacketDistributor.PLAYER.with(() -> player), packet);
    }
    
    public static void sendToAllPlayers(EventCounterPacket packet) {
        INSTANCE.send(PacketDistributor.ALL.noArg(), packet);
    }
}
