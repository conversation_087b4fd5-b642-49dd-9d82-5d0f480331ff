package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TimeDilationEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "time_dilation";
    }
    
    @Override
    public String getName() {
        return "§5§lTime Dilation!";
    }
    
    @Override
    public String getDescription() {
        return "Some areas move in slow motion, others super fast for 2 minutes";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§5§lTime becomes unstable... §7Reality warps around you!")));
        }
        
        // Apply time dilation effects for 2 minutes
        for (int i = 0; i < 120; i++) { // 2 minutes
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    // Randomly assign time dilation effects
                    int effect = random.nextInt(4);
                    
                    switch (effect) {
                        case 0: // Slow motion zone
                            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 60, 2)); // 3 seconds, level 3
                            player.addEffect(new MobEffectInstance(MobEffects.DIG_SLOWDOWN, 60, 2)); // Mining slowdown
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§7§lSlow Motion Zone - Time crawls...")));
                            break;
                            
                        case 1: // Super speed zone
                            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 60, 3)); // 3 seconds, level 4
                            player.addEffect(new MobEffectInstance(MobEffects.DIG_SPEED, 60, 3)); // Mining speed
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§a§lSpeed Zone - Time accelerates!")));
                            break;
                            
                        case 2: // Confusion zone (time feels weird)
                            player.addEffect(new MobEffectInstance(MobEffects.CONFUSION, 60, 0)); // 3 seconds
                            player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 30, 0)); // Brief slowdown
                            scheduler.schedule(() -> {
                                player.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SPEED, 30, 1)); // Then speed up
                            }, 1500L, TimeUnit.MILLISECONDS);
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§d§lTemporal Anomaly - Time stutters...")));
                            break;
                            
                        case 3: // Normal zone (brief respite)
                            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                net.minecraft.network.chat.Component.literal("§f§lNormal Zone - Time flows normally...")));
                            break;
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
        
        // Final message
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§5§lTime stabilizes... §7Reality returns to normal.")));
            }
        }, 120L, TimeUnit.SECONDS);
    }
}
