package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.Random;

public class SkyIslandsEvent extends RandomEvent {
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "sky_islands";
    }
    
    @Override
    public String getName() {
        return "§9§lSky Islands!";
    }
    
    @Override
    public String getDescription() {
        return "Floating dirt platforms appear above players";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Create 3-5 floating islands
            int islandCount = 3 + random.nextInt(3);
            for (int i = 0; i < islandCount; i++) {
                int x = playerPos.getX() + random.nextInt(60) - 30;
                int z = playerPos.getZ() + random.nextInt(60) - 30;
                int y = playerPos.getY() + 20 + random.nextInt(30);
                
                BlockPos centerPos = new BlockPos(x, y, z);
                
                // Create a small floating island (7x7 platform with some variation)
                for (int dx = -3; dx <= 3; dx++) {
                    for (int dz = -3; dz <= 3; dz++) {
                        // Create a circular-ish shape
                        if (dx * dx + dz * dz <= 9) {
                            BlockPos islandPos = centerPos.offset(dx, 0, dz);
                            
                            if (level.isInWorldBounds(islandPos) && level.getBlockState(islandPos).isAir()) {
                                level.setBlock(islandPos, Blocks.DIRT.defaultBlockState(), 3);
                                
                                // Add some grass on top
                                BlockPos grassPos = islandPos.above();
                                if (level.getBlockState(grassPos).isAir()) {
                                    level.setBlock(grassPos, Blocks.GRASS_BLOCK.defaultBlockState(), 3);
                                }
                                
                                // Occasionally add a flower
                                if (random.nextInt(10) == 0) {
                                    BlockPos flowerPos = grassPos.above();
                                    if (level.getBlockState(flowerPos).isAir()) {
                                        level.setBlock(flowerPos, Blocks.DANDELION.defaultBlockState(), 3);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
