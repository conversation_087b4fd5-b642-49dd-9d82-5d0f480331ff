package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.level.block.Blocks;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class XRayVisionEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static boolean xrayActive = false;
    private static final List<BlockPos> explosiveBlocks = new ArrayList<>();
    
    @Override
    public String getId() {
        return "xray_vision";
    }
    
    @Override
    public String getName() {
        return "§b§lX-Ray Vision!";
    }
    
    @Override
    public String getDescription() {
        return "Players can see through blocks with night vision for 1 minute";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        xrayActive = true;
        explosiveBlocks.clear();

        // Show warning message (don't tell them about explosions!)
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lYour vision becomes supernatural... §7You can see through everything!")));

            // Give night vision for X-ray effect
            player.addEffect(new MobEffectInstance(MobEffects.NIGHT_VISION, 60 * 20, 0)); // 1 minute

            // Also give glowing effect to see entities through walls
            player.addEffect(new MobEffectInstance(MobEffects.GLOWING, 60 * 20, 0)); // 1 minute
        }

        // Mark valuable blocks as explosive (secretly!)
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Find valuable blocks in a 50 block radius
            for (int x = -50; x <= 50; x++) {
                for (int y = -30; y <= 30; y++) {
                    for (int z = -50; z <= 50; z++) {
                        BlockPos checkPos = playerPos.offset(x, y, z);

                        if (level.isInWorldBounds(checkPos)) {
                            // Mark valuable blocks as explosive
                            if (level.getBlockState(checkPos).is(Blocks.DIAMOND_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.DEEPSLATE_DIAMOND_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.GOLD_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.DEEPSLATE_GOLD_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.IRON_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.DEEPSLATE_IRON_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.EMERALD_ORE) ||
                                level.getBlockState(checkPos).is(Blocks.DEEPSLATE_EMERALD_ORE)) {
                                explosiveBlocks.add(checkPos);
                            }
                        }
                    }
                }
            }
        }

        // Apply X-ray vision effect to all entities so players can see them
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                // Make all entities glow so they're visible through walls
                player.serverLevel().getAllEntities().forEach(entity -> {
                    if (entity instanceof net.minecraft.world.entity.LivingEntity livingEntity) {
                        livingEntity.addEffect(new MobEffectInstance(MobEffects.GLOWING, 60 * 20, 0));
                    }
                });
            }
        }, 1L, TimeUnit.SECONDS);

        // Register event listener for proximity explosions
        MinecraftForge.EVENT_BUS.register(new XRayEventHandler());

        // Stop X-ray effect after 1 minute
        scheduler.schedule(() -> {
            xrayActive = false;
            explosiveBlocks.clear();
            MinecraftForge.EVENT_BUS.unregister(XRayEventHandler.class);
        }, 60L, TimeUnit.SECONDS);
    }

    public static class XRayEventHandler {
        @SubscribeEvent
        public static void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!xrayActive || !(event.getEntity() instanceof ServerPlayer player)) return;

            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Check if player is near any explosive blocks
            List<BlockPos> toExplode = new ArrayList<>();
            for (BlockPos explosivePos : explosiveBlocks) {
                if (playerPos.distSqr(explosivePos) < 9) { // Within 3 blocks
                    toExplode.add(explosivePos);
                }
            }

            // Explode nearby blocks
            for (BlockPos explodePos : toExplode) {
                level.explode(null, explodePos.getX(), explodePos.getY(), explodePos.getZ(),
                            2.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.BLOCK);
                explosiveBlocks.remove(explodePos);
            }
        }
    }
}
