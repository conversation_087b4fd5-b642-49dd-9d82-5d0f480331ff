package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

public class LevitationLiftEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "levitation_lift";
    }
    
    @Override
    public String getName() {
        return "§d§lLevitation Lift!";
    }
    
    @Override
    public String getDescription() {
        return "All players levitate for 10 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.addEffect(new MobEffectInstance(MobEffects.LEVITATION, 10 * 20, 0)); // 10 seconds
        }
    }
}
