package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class RandomTeleportEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "random_teleport";
    }
    
    @Override
    public String getName() {
        return "§d§lRandom Teleport!";
    }
    
    @Override
    public String getDescription() {
        return "All players teleport to random locations (but teleport back together after 15 seconds)";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> players = new ArrayList<>(server.getPlayerList().getPlayers());
        if (players.isEmpty()) return;
        
        // Store original positions
        List<BlockPos> originalPositions = new ArrayList<>();
        List<ServerLevel> originalLevels = new ArrayList<>();
        
        for (ServerPlayer player : players) {
            originalPositions.add(player.blockPosition());
            originalLevels.add(player.serverLevel());
            
            // Teleport to random location
            ServerLevel level = player.serverLevel();
            
            // Find a safe random location within 1000 blocks
            BlockPos randomPos = findSafeLocation(level, player.blockPosition(), 1000);
            if (randomPos != null) {
                player.teleportTo(level, randomPos.getX() + 0.5, randomPos.getY(), randomPos.getZ() + 0.5, 
                                player.getYRot(), player.getXRot());
            }
        }
        
        // Teleport all players back together after 15 seconds
        scheduler.schedule(() -> {
            if (!players.isEmpty()) {
                // Choose the first player's original position as the meeting point
                BlockPos meetingPoint = originalPositions.get(0);
                ServerLevel meetingLevel = originalLevels.get(0);

                for (int i = 0; i < players.size(); i++) {
                    ServerPlayer player = players.get(i);
                    if (player.isAlive() && player.getServer() != null) {
                        // Teleport all players to the meeting point
                        player.teleportTo(meetingLevel, meetingPoint.getX() + 0.5, meetingPoint.getY(),
                                        meetingPoint.getZ() + 0.5, player.getYRot(), player.getXRot());
                    }
                }
            }
        }, 15, TimeUnit.SECONDS); // 15 seconds
    }
    
    private BlockPos findSafeLocation(ServerLevel level, BlockPos center, int radius) {
        for (int attempts = 0; attempts < 50; attempts++) {
            int x = center.getX() + random.nextInt(radius * 2) - radius;
            int z = center.getZ() + random.nextInt(radius * 2) - radius;
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
            
            BlockPos pos = new BlockPos(x, y, z);
            
            // Check if it's a safe location (solid ground, air above)
            if (level.isInWorldBounds(pos) && 
                !level.getBlockState(pos.below()).isAir() &&
                level.getBlockState(pos).isAir() &&
                level.getBlockState(pos.above()).isAir()) {
                return pos;
            }
        }
        
        // If no safe location found, return original position
        return center;
    }
}
