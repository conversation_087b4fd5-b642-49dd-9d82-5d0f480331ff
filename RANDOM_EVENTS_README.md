# Random Events Survival Mod

A Minecraft Forge mod for 1.20.1 that creates a survival challenge where random events happen every 30 seconds!

## Features

- 22 unique random events that each happen only once
- Events trigger every 30 seconds when the gamemode is active
- Server-side management (works in multiplayer)
- Persistent data (survives server restarts)
- Admin commands to control the events

## Events Included

### Survival Events (7)
1. **Instant Hunger** - All players lose all hunger instantly
2. **Health Swap** - All players' health gets randomly redistributed among them
3. **Half Heart Challenge** - All players reduced to 1 heart for 1 minute
4. **Blindness Blast** - All players blinded for 20 seconds
5. **Slow Motion** - All players get slowness for 1 minute
6. **Mining Fatigue** - All players get mining fatigue for 2 minutes
7. **Levitation Lift** - All players levitate for 10 seconds

### Environment Events (9)
8. **Lightning Storm** - Lightning strikes randomly around all players for 30 seconds
9. **Instant Night** - Time set to midnight
10. **Weather Chaos** - Weather changes every 10 seconds for 2 minutes
11. **Lava Rain** - Lava blocks fall from sky around players
12. **Ice Age** - All water in 50 block radius turns to ice
13. **Desert Storm** - Sand falls from sky, creates sand dunes
14. **Tree Explosion** - Trees randomly grow around all players
15. **Sky Islands** - Floating dirt platforms appear above players
16. **Underground Ocean** - Area below players fills with water

### Mob Events (1)
17. **Cow Chaos** - 20 cows spawn and push players around

### Item Events (1)
18. **Inventory Shuffle** - All players' inventories get randomly mixed up

### Movement Events (3)
19. **Random Teleport** - All players teleport to random locations (but teleport back together after 1.5 minutes)
20. **Player Swap** - All players swap positions with each other
21. **Quicksand** - Sand appears under players and they sink slowly

## Commands

All commands require OP level 2 (operator permissions):

- `/randomevents start` - Start the random events gamemode
- `/randomevents stop` - Stop the random events gamemode
- `/randomevents reset` - Reset the event pool (makes all events available again)
- `/randomevents status` - Show current status and event counts
- `/randomevents` - Show help with all available commands

## How to Play

1. Install the mod on your Minecraft 1.20.1 Forge server
2. Use `/randomevents start` to begin the challenge
3. Survive all 22 random events!
4. Each event happens only once until you reset with `/randomevents reset`
5. Events occur every 30 seconds automatically

## Installation

1. Download the mod JAR file from the `build/libs/` folder after building
2. Place it in your Minecraft server's `mods` folder
3. Start the server
4. Use the commands above to control the events

## Building from Source

1. Clone this repository
2. Run `./gradlew build` (or `gradlew.bat build` on Windows)
3. Find the built mod in `build/libs/`

## Compatibility

- Minecraft 1.20.1
- Forge 47.4.0+
- Server and client-side compatible

## Notes

- Events are designed to be challenging but fair
- The Random Teleport event automatically brings players back together after 1.5 minutes
- All events have been tested for multiplayer compatibility
- Event data persists across server restarts
- Some events may modify the world (like Lava Rain or Tree Explosion)
